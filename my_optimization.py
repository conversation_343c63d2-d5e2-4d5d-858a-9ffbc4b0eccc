import pandas as pd
import sqlite3
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt

# Connect to databases

db_name = 'cotacoes copy.db'

TOTAL_VALUE = 25000
VALUE_PER_ASSET = 5000
OFFER_SPREAD = 0.1
RISK_FREE_RATE = 0.1

def get_trade_system_data(
        window_size=10,
        n_liquidity_days = 90,
        n_liquidity_assets = 50,
        n_selected_assets = 10,
        db_name = 'cotacoes.db',
        max_window_size = 252,
):
    """
    Retrieves trade system dataframes:
    - df_assets_names: asset names
    - df_close_d_minus_2: close prices from D-2
    - df_close_d_minus_1: close prices from D-1
    - df_open_d_plus_0: open prices from D+0
    - moving_sum: the rolling sum of signals

    :param window_size: The rolling window size for the moving sum
    :return: pandas DataFrame with the requested columns
    """
    conn = sqlite3.connect(db_name)

    # Single consolidated SQL query
    query = f"""
    WITH liquidity_ranked AS (
        SELECT
            c_base.date,
            c_base.symbol,
            SUM(c_window.close * c_window.volume) AS total_financial_volume_rolling_days,
            ROW_NUMBER() OVER (PARTITION BY c_base.date ORDER BY SUM(c_window.close * c_window.volume) DESC) AS rn
        FROM
            cotacoes AS c_base
        JOIN
            cotacoes AS c_window ON c_window.symbol = c_base.symbol
            AND c_window.date BETWEEN DATE(c_base.date, '-' || ({n_liquidity_days} - 1) || ' day') AND c_base.date
        WHERE
            c_base.close * c_base.volume IS NOT NULL
            AND c_window.close * c_window.volume IS NOT NULL
        GROUP BY
            c_base.date, c_base.symbol
    ),
    thresholds AS (
        SELECT
            date,
            MIN(total_financial_volume_rolling_days) AS liquidity_volume_threshold
        FROM
            liquidity_ranked
        WHERE
            rn = {n_liquidity_assets}
        GROUP BY
            date
    ),
    asset_volumes AS (
        SELECT
            c.date,
            c.symbol,
            SUM(cw.close * cw.volume) AS total_financial_volume_rolling_days
        FROM
            cotacoes AS c
        JOIN
            cotacoes AS cw ON cw.symbol = c.symbol
            AND cw.date BETWEEN DATE(c.date, '-' || ({n_liquidity_days} - 1) || ' day') AND c.date
        WHERE
            c.close * c.volume IS NOT NULL
            AND cw.close * cw.volume IS NOT NULL
        GROUP BY
            c.date, c.symbol
    ),
    assets_above_threshold AS (
        SELECT
            a.date,
            a.symbol
        FROM
            asset_volumes AS a
        JOIN
            thresholds AS t ON a.date = t.date
        WHERE
            a.total_financial_volume_rolling_days >= t.liquidity_volume_threshold
    ),
    prices_with_lags AS (
        SELECT
            c.symbol,
            c.date,
            c.open,
            c.close,
            LAG(c.close, 1) OVER (PARTITION BY c.symbol ORDER BY c.date) AS close_d_minus_1,
            LAG(c.close, 2) OVER (PARTITION BY c.symbol ORDER BY c.date) AS close_d_minus_2
        FROM
            cotacoes AS c
    ),
    signals AS (
        SELECT
            p.symbol,
            p.date,
            p.open,
            p.close,
            p.close_d_minus_1,
            p.close_d_minus_2,
            CASE
                WHEN p.open > p.close_d_minus_1 THEN 1
                WHEN p.open < p.close_d_minus_1 THEN -1
                ELSE 0
            END AS signal
        FROM
            prices_with_lags AS p
    ),
    moving_sums AS (
        SELECT
            s.*,
            SUM(signal) OVER (
                PARTITION BY s.symbol
                ORDER BY s.date
                ROWS BETWEEN {window_size - 1} PRECEDING AND CURRENT ROW
            ) AS moving_sum
        FROM
            signals AS s
    ),
    selected_assets AS (
        SELECT
            m.date,
            m.symbol,
            m.close_d_minus_2,
            m.close_d_minus_1,
            m.open AS open_d_plus_0,
            m.moving_sum,
            ROW_NUMBER() OVER (PARTITION BY m.date ORDER BY m.moving_sum DESC) AS rn
        FROM
            moving_sums AS m
        JOIN
            assets_above_threshold AS a ON m.symbol = a.symbol AND m.date = a.date
    ),
    top_assets AS (
        SELECT
            *
        FROM
            selected_assets
        WHERE
            rn <= {n_selected_assets}
    )
    SELECT
        date,
        symbol,
        close_d_minus_2,
        close_d_minus_1,
        open_d_plus_0,
        moving_sum
    FROM
        top_assets
    ORDER BY
        date, symbol;
    """

    # Execute query and load results into a DataFrame
    df = pd.read_sql_query(query, conn, parse_dates=['date'])

    # Close DB connection
    conn.close()

    df_sorted = df.sort_values(by=["moving_sum", "date"], ascending=[False, True]).copy()

    # Count distinct dates
    distinct_dates = df_sorted['date'].drop_duplicates().reset_index(drop=True)

    # Get the date at position `max_window_size`
    if max_window_size >= len(distinct_dates):
        raise ValueError("max_window_size is larger than the number of distinct dates.")
    start_date = distinct_dates.iloc[max_window_size]

    # Filter the DataFrame to consider only dates above this date
    df_filtered = df_sorted[df_sorted['date'] >= start_date].copy()

    return df_filtered

def get_open_price_from_db(symbol, date):
    conn = sqlite3.connect(db_name)
    query = f"""
        SELECT open
        FROM cotacoes
        WHERE symbol = '{symbol}' AND date = '{date}'
        LIMIT 1
    """
    df = pd.read_sql_query(query, conn)
    conn.close()
    if not df.empty:
        return df.loc[0, 'open']
    else:
        return np.nan

df = get_trade_system_data(window_size=10, n_liquidity_days = 90,n_liquidity_assets = 50, n_selected_assets = 10)
available_capital = TOTAL_VALUE
open_positions = {}
trades = {}
for date, daily_assets in df.groupby('date'):
    for idx, asset in daily_assets.iterrows():
        if asset['symbol'] in open_positions or available_capital <= VALUE_PER_ASSET:
            continue  # skip if already open
        else:
            if np.isnan(asset['close_d_minus_2']):
                continue  # cannot buy
            asset_quantity = int(VALUE_PER_ASSET / (asset['close_d_minus_2'] * 100)) * 100
            if asset_quantity == 0:
                continue  # cannot buy
            order_value = asset['close_d_minus_2'] * (1 + OFFER_SPREAD)
            if order_value >= asset['close_d_minus_1']:
                open_positions[asset['symbol']] = {
                    'quantity': asset_quantity,
                    'buy_price': asset['close_d_minus_1'],
                    'sell_price': asset['open_d_plus_0'],
                    'date': date
                }
                available_capital -= asset_quantity * asset['close_d_minus_1']


    for symbol, position in open_positions.copy().items():
        sell_price = position['sell_price']
        if np.isnan(sell_price) and date == position['date']:
            continue  # cannot close
        elif np.isnan(sell_price):
            sell_price = get_open_price_from_db(symbol, date)
            if np.isnan(sell_price):
                continue  # cannot close
        available_capital += position['quantity'] * sell_price
        trades[(symbol, position['date'])] = {
            'symbol': symbol,
            'quantity': position['quantity'],
            'buy_price': position['buy_price'],
            'sell_price': sell_price,
            'profit': (sell_price - position['buy_price']) * position['quantity']
        }
        open_positions.pop(symbol)

if len(open_positions) > 0:
    print(f"open_positions: {open_positions}")

