"""
Financial Metrics Module for Trading System Optimization

This module provides comprehensive financial performance metrics for evaluating
trading strategies, including risk-adjusted returns, drawdown analysis, and
statistical measures.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class FinancialMetrics:
    """
    Comprehensive financial metrics calculator for trading strategy evaluation.
    """
    
    def __init__(self, risk_free_rate: float = 0.1):
        """
        Initialize the financial metrics calculator.
        
        Args:
            risk_free_rate: Annual risk-free rate (default: 10% as per your system)
        """
        self.risk_free_rate = risk_free_rate
    
    def calculate_returns(self, trades: Dict, initial_capital: float) -> pd.Series:
        """
        Calculate daily returns from trades dictionary.
        
        Args:
            trades: Dictionary of trades with profit information
            initial_capital: Starting capital amount
            
        Returns:
            pandas Series with daily returns indexed by date
        """
        if not trades:
            return pd.Series(dtype=float)
        
        # Convert trades to DataFrame
        trades_df = pd.DataFrame(trades).T
        trades_df.index = pd.MultiIndex.from_tuples(trades_df.index)
        trades_df['date'] = trades_df.index.get_level_values(1)
        
        # Group profits by date
        daily_profits = trades_df.groupby('date')['profit'].sum()
        
        # Calculate cumulative capital
        cumulative_capital = initial_capital + daily_profits.cumsum()
        
        # Calculate daily returns
        daily_returns = daily_profits / cumulative_capital.shift(1).fillna(initial_capital)
        
        return daily_returns.sort_index()
    
    def sharpe_ratio(self, returns: pd.Series) -> float:
        """
        Calculate Sharpe ratio.
        
        Args:
            returns: Series of daily returns
            
        Returns:
            Sharpe ratio (annualized)
        """
        if len(returns) == 0 or returns.std() == 0:
            return 0.0
        
        excess_returns = returns - (self.risk_free_rate / 252)  # Daily risk-free rate
        return np.sqrt(252) * excess_returns.mean() / returns.std()
    
    def sortino_ratio(self, returns: pd.Series) -> float:
        """
        Calculate Sortino ratio (downside deviation).
        
        Args:
            returns: Series of daily returns
            
        Returns:
            Sortino ratio (annualized)
        """
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - (self.risk_free_rate / 252)
        downside_returns = returns[returns < 0]
        
        if len(downside_returns) == 0 or downside_returns.std() == 0:
            return float('inf') if excess_returns.mean() > 0 else 0.0
        
        downside_deviation = np.sqrt(252) * downside_returns.std()
        return np.sqrt(252) * excess_returns.mean() / downside_deviation
    
    def maximum_drawdown(self, returns: pd.Series) -> Tuple[float, int]:
        """
        Calculate maximum drawdown and duration.
        
        Args:
            returns: Series of daily returns
            
        Returns:
            Tuple of (max_drawdown, max_duration_days)
        """
        if len(returns) == 0:
            return 0.0, 0
        
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        max_drawdown = drawdown.min()
        
        # Calculate drawdown duration
        is_drawdown = drawdown < 0
        drawdown_periods = []
        current_period = 0
        
        for is_dd in is_drawdown:
            if is_dd:
                current_period += 1
            else:
                if current_period > 0:
                    drawdown_periods.append(current_period)
                current_period = 0
        
        if current_period > 0:
            drawdown_periods.append(current_period)
        
        max_duration = max(drawdown_periods) if drawdown_periods else 0
        
        return abs(max_drawdown), max_duration
    
    def calmar_ratio(self, returns: pd.Series) -> float:
        """
        Calculate Calmar ratio (annual return / max drawdown).
        
        Args:
            returns: Series of daily returns
            
        Returns:
            Calmar ratio
        """
        if len(returns) == 0:
            return 0.0
        
        annual_return = (1 + returns).prod() ** (252 / len(returns)) - 1
        max_dd, _ = self.maximum_drawdown(returns)
        
        if max_dd == 0:
            return float('inf') if annual_return > 0 else 0.0
        
        return annual_return / max_dd
    
    def value_at_risk(self, returns: pd.Series, confidence: float = 0.05) -> float:
        """
        Calculate Value at Risk (VaR).
        
        Args:
            returns: Series of daily returns
            confidence: Confidence level (default: 5%)
            
        Returns:
            VaR value
        """
        if len(returns) == 0:
            return 0.0
        
        return np.percentile(returns, confidence * 100)
    
    def conditional_var(self, returns: pd.Series, confidence: float = 0.05) -> float:
        """
        Calculate Conditional Value at Risk (CVaR/Expected Shortfall).
        
        Args:
            returns: Series of daily returns
            confidence: Confidence level (default: 5%)
            
        Returns:
            CVaR value
        """
        if len(returns) == 0:
            return 0.0
        
        var = self.value_at_risk(returns, confidence)
        return returns[returns <= var].mean()
    
    def win_rate(self, trades: Dict) -> float:
        """
        Calculate win rate (percentage of profitable trades).
        
        Args:
            trades: Dictionary of trades
            
        Returns:
            Win rate as percentage
        """
        if not trades:
            return 0.0
        
        profits = [trade['profit'] for trade in trades.values()]
        winning_trades = sum(1 for profit in profits if profit > 0)
        
        return (winning_trades / len(profits)) * 100
    
    def profit_factor(self, trades: Dict) -> float:
        """
        Calculate profit factor (gross profit / gross loss).
        
        Args:
            trades: Dictionary of trades
            
        Returns:
            Profit factor
        """
        if not trades:
            return 0.0
        
        profits = [trade['profit'] for trade in trades.values()]
        gross_profit = sum(profit for profit in profits if profit > 0)
        gross_loss = abs(sum(profit for profit in profits if profit < 0))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    def average_trade_return(self, trades: Dict) -> float:
        """
        Calculate average return per trade.
        
        Args:
            trades: Dictionary of trades
            
        Returns:
            Average trade return
        """
        if not trades:
            return 0.0
        
        profits = [trade['profit'] for trade in trades.values()]
        return np.mean(profits)
    
    def trade_frequency(self, trades: Dict, total_days: int) -> float:
        """
        Calculate average trades per day.
        
        Args:
            trades: Dictionary of trades
            total_days: Total number of trading days
            
        Returns:
            Trades per day
        """
        if total_days == 0:
            return 0.0
        
        return len(trades) / total_days
    
    def calculate_comprehensive_metrics(self, trades: Dict, initial_capital: float, 
                                      total_days: int) -> Dict[str, float]:
        """
        Calculate all financial metrics for a trading strategy.
        
        Args:
            trades: Dictionary of trades
            initial_capital: Starting capital
            total_days: Total number of trading days
            
        Returns:
            Dictionary with all calculated metrics
        """
        returns = self.calculate_returns(trades, initial_capital)
        
        if len(returns) == 0:
            return {
                'total_return': 0.0,
                'annual_return': 0.0,
                'sharpe_ratio': 0.0,
                'sortino_ratio': 0.0,
                'max_drawdown': 0.0,
                'max_drawdown_duration': 0,
                'calmar_ratio': 0.0,
                'var_5': 0.0,
                'cvar_5': 0.0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'avg_trade_return': 0.0,
                'total_trades': 0,
                'trades_per_day': 0.0,
                'volatility': 0.0,
                'skewness': 0.0,
                'kurtosis': 0.0
            }
        
        # Calculate basic metrics
        total_return = (1 + returns).prod() - 1
        annual_return = (1 + total_return) ** (252 / len(returns)) - 1 if len(returns) > 0 else 0
        max_dd, max_dd_duration = self.maximum_drawdown(returns)
        
        # Calculate advanced metrics
        metrics = {
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': self.sharpe_ratio(returns),
            'sortino_ratio': self.sortino_ratio(returns),
            'max_drawdown': max_dd,
            'max_drawdown_duration': max_dd_duration,
            'calmar_ratio': self.calmar_ratio(returns),
            'var_5': self.value_at_risk(returns, 0.05),
            'cvar_5': self.conditional_var(returns, 0.05),
            'win_rate': self.win_rate(trades),
            'profit_factor': self.profit_factor(trades),
            'avg_trade_return': self.average_trade_return(trades),
            'total_trades': len(trades),
            'trades_per_day': self.trade_frequency(trades, total_days),
            'volatility': returns.std() * np.sqrt(252),
            'skewness': returns.skew(),
            'kurtosis': returns.kurtosis()
        }
        
        return metrics
    
    def risk_adjusted_score(self, metrics: Dict[str, float]) -> float:
        """
        Calculate a composite risk-adjusted performance score.
        
        This score combines multiple metrics with weights based on financial theory:
        - Sharpe ratio (40%): Risk-adjusted returns
        - Calmar ratio (25%): Return vs max drawdown
        - Win rate (15%): Consistency
        - Profit factor (10%): Efficiency
        - Sortino ratio (10%): Downside risk focus
        
        Args:
            metrics: Dictionary of calculated metrics
            
        Returns:
            Composite risk-adjusted score
        """
        weights = {
            'sharpe_ratio': 0.40,
            'calmar_ratio': 0.25,
            'win_rate': 0.15,
            'profit_factor': 0.10,
            'sortino_ratio': 0.10
        }
        
        # Normalize metrics to 0-1 scale
        normalized_metrics = {}
        
        # Sharpe ratio: normalize using sigmoid function
        sharpe = metrics.get('sharpe_ratio', 0)
        normalized_metrics['sharpe_ratio'] = 1 / (1 + np.exp(-sharpe))
        
        # Calmar ratio: normalize using sigmoid function
        calmar = metrics.get('calmar_ratio', 0)
        if calmar == float('inf'):
            normalized_metrics['calmar_ratio'] = 1.0
        else:
            normalized_metrics['calmar_ratio'] = 1 / (1 + np.exp(-calmar))
        
        # Win rate: already in percentage, normalize to 0-1
        win_rate = metrics.get('win_rate', 0)
        normalized_metrics['win_rate'] = min(win_rate / 100, 1.0)
        
        # Profit factor: normalize using log function
        pf = metrics.get('profit_factor', 0)
        if pf == float('inf'):
            normalized_metrics['profit_factor'] = 1.0
        elif pf <= 0:
            normalized_metrics['profit_factor'] = 0.0
        else:
            normalized_metrics['profit_factor'] = min(np.log(pf + 1) / np.log(3), 1.0)
        
        # Sortino ratio: normalize using sigmoid function
        sortino = metrics.get('sortino_ratio', 0)
        if sortino == float('inf'):
            normalized_metrics['sortino_ratio'] = 1.0
        else:
            normalized_metrics['sortino_ratio'] = 1 / (1 + np.exp(-sortino))
        
        # Calculate weighted score
        score = sum(weights[metric] * normalized_metrics[metric] 
                   for metric in weights.keys())
        
        return score
