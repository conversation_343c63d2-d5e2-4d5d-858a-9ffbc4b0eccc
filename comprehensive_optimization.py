"""
Comprehensive Parameter Optimization Script

This script executes a sophisticated multi-phase optimization using financial theory
and the extended parameter ranges specified by the user.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from parameter_optimizer import ParameterOptimizer
from advanced_optimization_config import AdvancedOptimizationConfig
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


def main():
    """Main comprehensive optimization execution function."""
    
    print("="*80)
    print("COMPREHENSIVE TRADING SYSTEM PARAMETER OPTIMIZATION")
    print("="*80)
    print("Using Extended Parameter Ranges:")
    print("  window_size: 1-252")
    print("  n_liquidity_days: 5-252") 
    print("  n_liquidity_assets: 20-300")
    print("  n_selected_assets: 1-20")
    print("="*80)
    print()
    
    # Initialize optimizer and advanced config
    optimizer = ParameterOptimizer(
        db_name='cotacoes copy.db',
        total_value=25000,
        value_per_asset=5000,
        offer_spread=0.1,
        risk_free_rate=0.1
    )
    
    config = AdvancedOptimizationConfig()
    
    # Current parameters baseline
    current_params = {
        'window_size': 10,
        'n_liquidity_days': 90,
        'n_liquidity_assets': 50,
        'n_selected_assets': 10
    }
    
    print("PHASE 0: Baseline Performance")
    print("-" * 50)
    print("Current Parameters:")
    for param, value in current_params.items():
        print(f"  {param}: {value}")
    print()
    
    # Benchmark current parameters
    current_trades, current_metrics = optimizer.run_backtest(current_params)
    
    print("Current Parameters Performance:")
    print(f"  Total Return: {current_metrics['total_return']:.2%}")
    print(f"  Annual Return: {current_metrics['annual_return']:.2%}")
    print(f"  Sharpe Ratio: {current_metrics['sharpe_ratio']:.3f}")
    print(f"  Calmar Ratio: {current_metrics['calmar_ratio']:.3f}")
    print(f"  Max Drawdown: {current_metrics['max_drawdown']:.2%}")
    print(f"  Win Rate: {current_metrics['win_rate']:.1f}%")
    print(f"  Total Trades: {current_metrics['total_trades']}")
    print(f"  Risk-Adjusted Score: {current_metrics['risk_adjusted_score']:.4f}")
    print()
    
    # PHASE 1: Theoretical Exploration
    print("PHASE 1: Theoretical Parameter Exploration")
    print("-" * 50)
    print("Exploring parameter combinations based on financial theory...")
    
    # Get theoretical combinations
    theoretical_combinations = config.get_theoretical_combinations('comprehensive')
    print(f"Generated {len(theoretical_combinations)} theoretical combinations")
    
    # Limit to manageable size for demonstration
    if len(theoretical_combinations) > 500:
        print(f"Limiting to top 500 combinations for performance...")
        # Sample strategically - take every nth combination
        step = len(theoretical_combinations) // 500
        theoretical_combinations = theoretical_combinations[::step][:500]
    
    print(f"Testing {len(theoretical_combinations)} parameter combinations...")
    
    # Run optimization on theoretical combinations
    theoretical_results = []
    
    print("Running theoretical optimization...")
    for i, params in enumerate(theoretical_combinations):
        if i % 50 == 0:
            print(f"  Progress: {i}/{len(theoretical_combinations)} ({i/len(theoretical_combinations)*100:.1f}%)")
        
        try:
            trades, metrics = optimizer.run_backtest(params)
            result = {**params, **metrics}
            theoretical_results.append(result)
        except Exception as e:
            print(f"  Error with params {params}: {str(e)}")
            continue
    
    if not theoretical_results:
        print("ERROR: No valid results from theoretical exploration!")
        return
    
    # Convert to DataFrame and analyze
    theoretical_df = pd.DataFrame(theoretical_results)
    
    # Filter by minimum trades threshold
    min_trades = 50
    theoretical_df = theoretical_df[theoretical_df['total_trades'] >= min_trades].copy()
    
    if theoretical_df.empty:
        print(f"WARNING: No combinations met minimum {min_trades} trades threshold!")
        theoretical_df = pd.DataFrame(theoretical_results)
    
    # Sort by risk-adjusted score
    theoretical_df = theoretical_df.sort_values('risk_adjusted_score', ascending=False)
    
    print(f"\nPhase 1 Results:")
    print(f"  Valid combinations tested: {len(theoretical_df)}")
    print(f"  Best risk-adjusted score: {theoretical_df.iloc[0]['risk_adjusted_score']:.4f}")
    
    best_theoretical = theoretical_df.iloc[0]
    improvement_pct = ((best_theoretical['risk_adjusted_score'] / current_metrics['risk_adjusted_score']) - 1) * 100
    print(f"  Improvement vs baseline: {improvement_pct:.1f}%")
    
    # PHASE 2: Top Performers Refinement
    print("\nPHASE 2: Top Performers Refinement")
    print("-" * 50)
    
    # Get top 10% performers
    top_performers = theoretical_df.head(max(10, len(theoretical_df) // 10))
    
    print("Top 10 Parameter Combinations from Phase 1:")
    print("-" * 60)
    print(f"{'Rank':<4} {'WS':<3} {'LD':<4} {'LA':<4} {'SA':<3} {'Score':<8} {'Sharpe':<8} {'Trades':<6}")
    print("-" * 60)
    
    for i, (_, row) in enumerate(top_performers.head(10).iterrows(), 1):
        print(f"{i:<4} {int(row['window_size']):<3} {int(row['n_liquidity_days']):<4} "
              f"{int(row['n_liquidity_assets']):<4} {int(row['n_selected_assets']):<3} "
              f"{row['risk_adjusted_score']:<8.4f} {row['sharpe_ratio']:<8.3f} {int(row['total_trades']):<6}")
    
    # Analyze parameter patterns in top performers
    print(f"\nParameter Analysis of Top Performers:")
    print(f"  Window Size Range: {top_performers['window_size'].min()}-{top_performers['window_size'].max()}")
    print(f"  Liquidity Days Range: {top_performers['n_liquidity_days'].min()}-{top_performers['n_liquidity_days'].max()}")
    print(f"  Liquidity Assets Range: {top_performers['n_liquidity_assets'].min()}-{top_performers['n_liquidity_assets'].max()}")
    print(f"  Selected Assets Range: {top_performers['n_selected_assets'].min()}-{top_performers['n_selected_assets'].max()}")
    
    # PHASE 3: Fine-tuning around best parameters
    print("\nPHASE 3: Fine-tuning Optimization")
    print("-" * 50)
    
    best_params = {
        'window_size': int(best_theoretical['window_size']),
        'n_liquidity_days': int(best_theoretical['n_liquidity_days']),
        'n_liquidity_assets': int(best_theoretical['n_liquidity_assets']),
        'n_selected_assets': int(best_theoretical['n_selected_assets'])
    }
    
    print(f"Fine-tuning around best parameters: {best_params}")
    
    # Generate fine-tuning variations
    fine_tune_combinations = generate_fine_tune_variations(best_params)
    print(f"Testing {len(fine_tune_combinations)} fine-tuning variations...")
    
    fine_tune_results = []
    for params in fine_tune_combinations:
        try:
            trades, metrics = optimizer.run_backtest(params)
            result = {**params, **metrics}
            fine_tune_results.append(result)
        except Exception as e:
            continue
    
    if fine_tune_results:
        fine_tune_df = pd.DataFrame(fine_tune_results)
        fine_tune_df = fine_tune_df[fine_tune_df['total_trades'] >= min_trades].copy()
        
        if not fine_tune_df.empty:
            fine_tune_df = fine_tune_df.sort_values('risk_adjusted_score', ascending=False)
            best_fine_tune = fine_tune_df.iloc[0]
            
            print(f"Fine-tuning Results:")
            print(f"  Best fine-tuned score: {best_fine_tune['risk_adjusted_score']:.4f}")
            
            # Use best fine-tuned if better, otherwise use best theoretical
            if best_fine_tune['risk_adjusted_score'] > best_theoretical['risk_adjusted_score']:
                final_best = best_fine_tune
                print(f"  Fine-tuning improved performance!")
            else:
                final_best = best_theoretical
                print(f"  Original best parameters remain optimal")
        else:
            final_best = best_theoretical
    else:
        final_best = best_theoretical
    
    # PHASE 4: Validation and Analysis
    print("\nPHASE 4: Validation and Analysis")
    print("-" * 50)
    
    final_params = {
        'window_size': int(final_best['window_size']),
        'n_liquidity_days': int(final_best['n_liquidity_days']),
        'n_liquidity_assets': int(final_best['n_liquidity_assets']),
        'n_selected_assets': int(final_best['n_selected_assets'])
    }
    
    print("Final Optimal Parameters:")
    for param, value in final_params.items():
        print(f"  {param}: {value}")
    
    print(f"\nFinal Performance Metrics:")
    print(f"  Total Return: {final_best['total_return']:.2%}")
    print(f"  Annual Return: {final_best['annual_return']:.2%}")
    print(f"  Sharpe Ratio: {final_best['sharpe_ratio']:.3f}")
    print(f"  Calmar Ratio: {final_best['calmar_ratio']:.3f}")
    print(f"  Max Drawdown: {final_best['max_drawdown']:.2%}")
    print(f"  Win Rate: {final_best['win_rate']:.1f}%")
    print(f"  Total Trades: {int(final_best['total_trades'])}")
    print(f"  Risk-Adjusted Score: {final_best['risk_adjusted_score']:.4f}")
    
    # Calculate improvement
    final_improvement = ((final_best['risk_adjusted_score'] / current_metrics['risk_adjusted_score']) - 1) * 100
    print(f"\nOverall Improvement vs Baseline: {final_improvement:.1f}%")
    
    # Walk-forward validation
    print(f"\nWalk-Forward Validation:")
    wf_results = optimizer.walk_forward_analysis(final_params, n_periods=4)
    
    if 'error' not in wf_results:
        print(f"  Average Return: {wf_results['avg_return']:.2%}")
        print(f"  Return Volatility: {wf_results['std_return']:.2%}")
        print(f"  Average Sharpe: {wf_results['avg_sharpe']:.3f}")
        print(f"  Consistency Score: {wf_results['consistency_score']:.1%}")
        print(f"  Worst Drawdown: {wf_results['worst_max_dd']:.2%}")
    else:
        print(f"  Walk-forward analysis failed: {wf_results['error']}")
    
    # Generate comprehensive report
    print("\nPHASE 5: Report Generation")
    print("-" * 50)
    
    generate_comprehensive_report(
        current_params, current_metrics, final_params, final_best,
        theoretical_df, top_performers, wf_results
    )
    
    # Create visualizations
    create_comprehensive_plots(theoretical_df, top_performers, current_metrics, final_best)
    
    # Final recommendations
    print("\n" + "="*80)
    print("FINAL RECOMMENDATIONS")
    print("="*80)
    
    if final_improvement > 15:
        print("🎯 STRONG RECOMMENDATION: Adopt the optimized parameters immediately!")
        print(f"   Expected improvement: {final_improvement:.1f}%")
        print("   The optimization found significantly better parameters.")
    elif final_improvement > 5:
        print("✅ MODERATE RECOMMENDATION: Consider adopting optimized parameters")
        print(f"   Expected improvement: {final_improvement:.1f}%")
        print("   Test in paper trading before full implementation.")
    elif final_improvement > 0:
        print("⚠️  WEAK RECOMMENDATION: Minor improvement possible")
        print(f"   Expected improvement: {final_improvement:.1f}%")
        print("   Current parameters are already quite good.")
    else:
        print("❌ NO RECOMMENDATION: Current parameters are optimal")
        print("   The optimization confirms your current parameters are excellent.")
    
    print(f"\nOptimal Parameters Summary:")
    print(f"  window_size: {final_params['window_size']} (was {current_params['window_size']})")
    print(f"  n_liquidity_days: {final_params['n_liquidity_days']} (was {current_params['n_liquidity_days']})")
    print(f"  n_liquidity_assets: {final_params['n_liquidity_assets']} (was {current_params['n_liquidity_assets']})")
    print(f"  n_selected_assets: {final_params['n_selected_assets']} (was {current_params['n_selected_assets']})")
    
    # Risk warnings
    print(f"\n⚠️  RISK ASSESSMENT:")
    if final_best['max_drawdown'] > 0.20:
        print("   HIGH RISK: Maximum drawdown > 20%")
    elif final_best['max_drawdown'] > 0.15:
        print("   MODERATE RISK: Maximum drawdown 15-20%")
    else:
        print("   LOW RISK: Maximum drawdown < 15%")
    
    if final_best['sharpe_ratio'] < 1.0:
        print("   WARNING: Sharpe ratio < 1.0 indicates poor risk-adjusted returns")
    elif final_best['sharpe_ratio'] > 2.0:
        print("   EXCELLENT: Sharpe ratio > 2.0 indicates exceptional performance")
    
    print("\n" + "="*80)
    print("OPTIMIZATION COMPLETE!")
    print("="*80)


def generate_fine_tune_variations(best_params):
    """Generate fine-tuning variations around best parameters."""
    variations = []
    
    # Small variations around each parameter
    for ws_delta in [-2, -1, 0, 1, 2]:
        for ld_delta in [-10, -5, 0, 5, 10]:
            for la_delta in [-10, -5, 0, 5, 10]:
                for sa_delta in [-2, -1, 0, 1, 2]:
                    new_params = {
                        'window_size': max(1, min(252, best_params['window_size'] + ws_delta)),
                        'n_liquidity_days': max(5, min(252, best_params['n_liquidity_days'] + ld_delta)),
                        'n_liquidity_assets': max(20, min(300, best_params['n_liquidity_assets'] + la_delta)),
                        'n_selected_assets': max(1, min(20, best_params['n_selected_assets'] + sa_delta))
                    }
                    
                    # Validate constraints
                    if (new_params['n_selected_assets'] <= new_params['n_liquidity_assets'] and
                        new_params['window_size'] <= new_params['n_liquidity_days']):
                        variations.append(new_params)
    
    return variations


def generate_comprehensive_report(current_params, current_metrics, final_params, final_best,
                                theoretical_df, top_performers, wf_results):
    """Generate comprehensive optimization report."""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'comprehensive_optimization_report_{timestamp}.txt'
    
    with open(report_file, 'w') as f:
        f.write("COMPREHENSIVE TRADING SYSTEM PARAMETER OPTIMIZATION REPORT\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Parameter Ranges Tested:\n")
        f.write(f"  window_size: 1-252\n")
        f.write(f"  n_liquidity_days: 5-252\n")
        f.write(f"  n_liquidity_assets: 20-300\n")
        f.write(f"  n_selected_assets: 1-20\n\n")
        
        # Baseline performance
        f.write("BASELINE PARAMETERS\n")
        f.write("-" * 30 + "\n")
        for param, value in current_params.items():
            f.write(f"{param}: {value}\n")
        f.write(f"\nBaseline Performance:\n")
        for metric, value in current_metrics.items():
            if isinstance(value, float):
                if 'rate' in metric or 'return' in metric:
                    f.write(f"  {metric}: {value:.2%}\n")
                else:
                    f.write(f"  {metric}: {value:.4f}\n")
            else:
                f.write(f"  {metric}: {value}\n")
        f.write("\n")
        
        # Optimized parameters
        f.write("OPTIMIZED PARAMETERS\n")
        f.write("-" * 30 + "\n")
        for param, value in final_params.items():
            f.write(f"{param}: {value}\n")
        f.write(f"\nOptimized Performance:\n")
        for metric, value in final_best.items():
            if metric in ['window_size', 'n_liquidity_days', 'n_liquidity_assets', 'n_selected_assets']:
                continue
            if isinstance(value, float):
                if 'rate' in metric or 'return' in metric:
                    f.write(f"  {metric}: {value:.2%}\n")
                else:
                    f.write(f"  {metric}: {value:.4f}\n")
            else:
                f.write(f"  {metric}: {value}\n")
        
        improvement = ((final_best['risk_adjusted_score'] / current_metrics['risk_adjusted_score']) - 1) * 100
        f.write(f"\nOverall Improvement: {improvement:.1f}%\n\n")
        
        # Top performers analysis
        f.write("TOP PERFORMERS ANALYSIS\n")
        f.write("-" * 30 + "\n")
        f.write(f"Total combinations tested: {len(theoretical_df)}\n")
        f.write(f"Top 10 parameter ranges:\n")
        f.write(f"  Window Size: {top_performers['window_size'].min()}-{top_performers['window_size'].max()}\n")
        f.write(f"  Liquidity Days: {top_performers['n_liquidity_days'].min()}-{top_performers['n_liquidity_days'].max()}\n")
        f.write(f"  Liquidity Assets: {top_performers['n_liquidity_assets'].min()}-{top_performers['n_liquidity_assets'].max()}\n")
        f.write(f"  Selected Assets: {top_performers['n_selected_assets'].min()}-{top_performers['n_selected_assets'].max()}\n\n")
        
        # Walk-forward results
        if 'error' not in wf_results:
            f.write("WALK-FORWARD VALIDATION\n")
            f.write("-" * 30 + "\n")
            f.write(f"Average Return: {wf_results['avg_return']:.2%}\n")
            f.write(f"Return Volatility: {wf_results['std_return']:.2%}\n")
            f.write(f"Average Sharpe: {wf_results['avg_sharpe']:.3f}\n")
            f.write(f"Consistency Score: {wf_results['consistency_score']:.1%}\n")
            f.write(f"Worst Drawdown: {wf_results['worst_max_dd']:.2%}\n\n")
        
        # Top 20 combinations
        f.write("TOP 20 PARAMETER COMBINATIONS\n")
        f.write("-" * 40 + "\n")
        f.write(f"{'Rank':<4} {'WS':<3} {'LD':<4} {'LA':<4} {'SA':<3} {'Score':<8} {'Sharpe':<8}\n")
        f.write("-" * 40 + "\n")
        for i, (_, row) in enumerate(theoretical_df.head(20).iterrows(), 1):
            f.write(f"{i:2d}. {int(row['window_size']):3d} {int(row['n_liquidity_days']):4d} "
                   f"{int(row['n_liquidity_assets']):4d} {int(row['n_selected_assets']):3d} "
                   f"{row['risk_adjusted_score']:8.4f} {row['sharpe_ratio']:8.3f}\n")
    
    print(f"Comprehensive report saved to: {report_file}")


def create_comprehensive_plots(theoretical_df, top_performers, current_metrics, final_best):
    """Create comprehensive visualization plots."""
    
    if theoretical_df.empty:
        print("No data to plot.")
        return
    
    # Create figure with subplots
    fig, axes = plt.subplots(3, 3, figsize=(20, 15))
    fig.suptitle('Comprehensive Parameter Optimization Results', fontsize=16, fontweight='bold')
    
    # Plot 1: Risk-Return Scatter
    ax1 = axes[0, 0]
    scatter1 = ax1.scatter(theoretical_df['max_drawdown'], theoretical_df['annual_return'], 
                          c=theoretical_df['risk_adjusted_score'], cmap='viridis', alpha=0.6)
    ax1.scatter(current_metrics['max_drawdown'], current_metrics['annual_return'], 
               color='red', s=100, marker='x', label='Current')
    ax1.scatter(final_best['max_drawdown'], final_best['annual_return'], 
               color='gold', s=100, marker='*', label='Optimal')
    ax1.set_xlabel('Max Drawdown')
    ax1.set_ylabel('Annual Return')
    ax1.set_title('Risk-Return Profile')
    ax1.legend()
    plt.colorbar(scatter1, ax=ax1, label='Risk-Adjusted Score')
    
    # Plot 2: Parameter Distribution - Window Size
    ax2 = axes[0, 1]
    ax2.hist(theoretical_df['window_size'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(current_metrics.get('window_size', 10), color='red', linestyle='--', label='Current')
    ax2.axvline(final_best['window_size'], color='gold', linestyle='-', linewidth=2, label='Optimal')
    ax2.set_xlabel('Window Size')
    ax2.set_ylabel('Frequency')
    ax2.set_title('Window Size Distribution')
    ax2.legend()
    
    # Plot 3: Parameter Distribution - Liquidity Days
    ax3 = axes[0, 2]
    ax3.hist(theoretical_df['n_liquidity_days'], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
    ax3.axvline(current_metrics.get('n_liquidity_days', 90), color='red', linestyle='--', label='Current')
    ax3.axvline(final_best['n_liquidity_days'], color='gold', linestyle='-', linewidth=2, label='Optimal')
    ax3.set_xlabel('Liquidity Days')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Liquidity Days Distribution')
    ax3.legend()
    
    # Plot 4: Sharpe vs Score
    ax4 = axes[1, 0]
    scatter2 = ax4.scatter(theoretical_df['sharpe_ratio'], theoretical_df['risk_adjusted_score'], 
                          c=theoretical_df['total_trades'], cmap='plasma', alpha=0.6)
    ax4.scatter(current_metrics['sharpe_ratio'], current_metrics['risk_adjusted_score'], 
               color='red', s=100, marker='x', label='Current')
    ax4.scatter(final_best['sharpe_ratio'], final_best['risk_adjusted_score'], 
               color='gold', s=100, marker='*', label='Optimal')
    ax4.set_xlabel('Sharpe Ratio')
    ax4.set_ylabel('Risk-Adjusted Score')
    ax4.set_title('Sharpe vs Risk-Adjusted Score')
    ax4.legend()
    plt.colorbar(scatter2, ax=ax4, label='Total Trades')
    
    # Plot 5: Top Performers Heatmap
    ax5 = axes[1, 1]
    if len(top_performers) >= 10:
        metrics_cols = ['risk_adjusted_score', 'sharpe_ratio', 'calmar_ratio', 'win_rate']
        top_10_metrics = top_performers.head(10)[metrics_cols]
        sns.heatmap(top_10_metrics.T, annot=True, fmt='.3f', cmap='RdYlGn', ax=ax5)
        ax5.set_title('Top 10 Performance Heatmap')
        ax5.set_xlabel('Rank')
    
    # Plot 6: Asset Selection Analysis
    ax6 = axes[1, 2]
    scatter3 = ax6.scatter(theoretical_df['n_liquidity_assets'], theoretical_df['n_selected_assets'],
                          c=theoretical_df['risk_adjusted_score'], cmap='coolwarm', alpha=0.6)
    ax6.scatter(current_metrics.get('n_liquidity_assets', 50), current_metrics.get('n_selected_assets', 10), 
               color='red', s=100, marker='x', label='Current')
    ax6.scatter(final_best['n_liquidity_assets'], final_best['n_selected_assets'], 
               color='gold', s=100, marker='*', label='Optimal')
    ax6.set_xlabel('Liquidity Assets')
    ax6.set_ylabel('Selected Assets')
    ax6.set_title('Asset Selection Parameters')
    ax6.legend()
    plt.colorbar(scatter3, ax=ax6, label='Risk-Adjusted Score')
    
    # Plot 7: Score Distribution
    ax7 = axes[2, 0]
    ax7.hist(theoretical_df['risk_adjusted_score'], bins=30, alpha=0.7, color='orange', edgecolor='black')
    ax7.axvline(current_metrics['risk_adjusted_score'], color='red', linestyle='--', 
                label=f'Current: {current_metrics["risk_adjusted_score"]:.3f}')
    ax7.axvline(final_best['risk_adjusted_score'], color='gold', linestyle='-', linewidth=2,
                label=f'Optimal: {final_best["risk_adjusted_score"]:.3f}')
    ax7.set_xlabel('Risk-Adjusted Score')
    ax7.set_ylabel('Frequency')
    ax7.set_title('Risk-Adjusted Score Distribution')
    ax7.legend()
    
    # Plot 8: Win Rate vs Profit Factor
    ax8 = axes[2, 1]
    scatter4 = ax8.scatter(theoretical_df['win_rate'], theoretical_df['profit_factor'],
                          c=theoretical_df['risk_adjusted_score'], cmap='viridis', alpha=0.6)
    ax8.scatter(current_metrics['win_rate'], current_metrics['profit_factor'], 
               color='red', s=100, marker='x', label='Current')
    ax8.scatter(final_best['win_rate'], final_best['profit_factor'], 
               color='gold', s=100, marker='*', label='Optimal')
    ax8.set_xlabel('Win Rate (%)')
    ax8.set_ylabel('Profit Factor')
    ax8.set_title('Win Rate vs Profit Factor')
    ax8.legend()
    plt.colorbar(scatter4, ax=ax8, label='Risk-Adjusted Score')
    
    # Plot 9: Parameter Correlation Matrix
    ax9 = axes[2, 2]
    param_cols = ['window_size', 'n_liquidity_days', 'n_liquidity_assets', 'n_selected_assets']
    corr_matrix = theoretical_df[param_cols + ['risk_adjusted_score']].corr()
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=ax9)
    ax9.set_title('Parameter Correlation Matrix')
    
    plt.tight_layout()
    
    # Save plot
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    plot_file = f'comprehensive_optimization_plots_{timestamp}.png'
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Comprehensive plots saved to: {plot_file}")


if __name__ == "__main__":
    main()
