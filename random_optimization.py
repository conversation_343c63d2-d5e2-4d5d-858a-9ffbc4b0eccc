"""
Random Parameter Optimization with Incremental Saving

This script uses random sampling within the specified parameter ranges
and saves results after each backtest for continuous evaluation.
"""

import pandas as pd
import numpy as np
import random
import json
import os
from datetime import datetime
from parameter_optimizer import ParameterOptimizer
import warnings
warnings.filterwarnings('ignore')


class RandomOptimizer:
    """
    Random parameter optimizer with incremental saving.
    """
    
    def __init__(self, db_name='cotacoes copy.db', results_file=None):
        """Initialize the random optimizer."""
        self.optimizer = ParameterOptimizer(
            db_name=db_name,
            total_value=25000,
            value_per_asset=5000,
            offer_spread=0.1,
            risk_free_rate=0.1
        )
        
        # Parameter ranges
        self.param_ranges = {
            'window_size': (1, 252),
            'n_liquidity_days': (5, 252),
            'n_liquidity_assets': (20, 300),
            'n_selected_assets': (1, 20)
        }
        
        # Results file
        if results_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.results_file = f'random_optimization_results_{timestamp}.csv'
        else:
            self.results_file = results_file
        
        # Initialize results file if it doesn't exist
        if not os.path.exists(self.results_file):
            self._initialize_results_file()
        
        print(f"Results will be saved to: {self.results_file}")
    
    def _initialize_results_file(self):
        """Initialize the results CSV file with headers."""
        headers = [
            'iteration', 'timestamp', 'window_size', 'n_liquidity_days', 
            'n_liquidity_assets', 'n_selected_assets', 'total_return', 
            'annual_return', 'sharpe_ratio', 'sortino_ratio', 'max_drawdown',
            'max_drawdown_duration', 'calmar_ratio', 'var_5', 'cvar_5',
            'win_rate', 'profit_factor', 'avg_trade_return', 'total_trades',
            'trades_per_day', 'volatility', 'skewness', 'kurtosis',
            'risk_adjusted_score', 'execution_time'
        ]
        
        df = pd.DataFrame(columns=headers)
        df.to_csv(self.results_file, index=False)
        print(f"Initialized results file: {self.results_file}")
    
    def generate_random_params(self):
        """Generate random parameters within specified ranges."""
        params = {}
        
        # Generate random values
        params['window_size'] = random.randint(*self.param_ranges['window_size'])
        params['n_liquidity_days'] = random.randint(*self.param_ranges['n_liquidity_days'])
        params['n_liquidity_assets'] = random.randint(*self.param_ranges['n_liquidity_assets'])
        params['n_selected_assets'] = random.randint(*self.param_ranges['n_selected_assets'])
        
        # Apply constraints
        # Ensure n_selected_assets <= n_liquidity_assets
        if params['n_selected_assets'] > params['n_liquidity_assets']:
            params['n_selected_assets'] = min(params['n_liquidity_assets'], 20)
        
        # Ensure window_size <= n_liquidity_days (for signal stability)
        if params['window_size'] > params['n_liquidity_days']:
            params['window_size'] = min(params['n_liquidity_days'], 252)
        
        return params
    
    def run_single_optimization(self, iteration):
        """Run a single optimization iteration."""
        start_time = datetime.now()
        
        # Generate random parameters
        params = self.generate_random_params()
        
        try:
            # Run backtest
            trades, metrics = self.optimizer.run_backtest(params)
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Prepare result
            result = {
                'iteration': iteration,
                'timestamp': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'execution_time': execution_time,
                **params,
                **metrics
            }
            
            # Save result immediately
            self._save_result(result)
            
            return result, True
            
        except Exception as e:
            print(f"Error in iteration {iteration} with params {params}: {str(e)}")
            
            # Save failed result
            execution_time = (datetime.now() - start_time).total_seconds()
            failed_result = {
                'iteration': iteration,
                'timestamp': start_time.strftime('%Y-%m-%d %H:%M:%S'),
                'execution_time': execution_time,
                **params,
                **{key: 0.0 for key in ['total_return', 'annual_return', 'sharpe_ratio', 
                                       'sortino_ratio', 'max_drawdown', 'max_drawdown_duration',
                                       'calmar_ratio', 'var_5', 'cvar_5', 'win_rate', 
                                       'profit_factor', 'avg_trade_return', 'total_trades',
                                       'trades_per_day', 'volatility', 'skewness', 'kurtosis',
                                       'risk_adjusted_score']}
            }
            
            self._save_result(failed_result)
            return failed_result, False
    
    def _save_result(self, result):
        """Save a single result to the CSV file."""
        df = pd.DataFrame([result])
        df.to_csv(self.results_file, mode='a', header=False, index=False)
    
    def run_optimization(self, max_iterations=1000, progress_interval=10):
        """
        Run random optimization with specified number of iterations.
        
        Args:
            max_iterations: Maximum number of iterations to run
            progress_interval: Show progress every N iterations
        """
        print(f"Starting random optimization with {max_iterations} iterations...")
        print(f"Parameter ranges:")
        for param, (min_val, max_val) in self.param_ranges.items():
            print(f"  {param}: {min_val}-{max_val}")
        print()
        
        successful_runs = 0
        failed_runs = 0
        best_score = 0.0
        best_params = None
        
        for i in range(1, max_iterations + 1):
            result, success = self.run_single_optimization(i)
            
            if success:
                successful_runs += 1
                if result['risk_adjusted_score'] > best_score:
                    best_score = result['risk_adjusted_score']
                    best_params = {k: result[k] for k in ['window_size', 'n_liquidity_days', 
                                                         'n_liquidity_assets', 'n_selected_assets']}
            else:
                failed_runs += 1
            
            # Show progress
            if i % progress_interval == 0:
                success_rate = (successful_runs / i) * 100
                print(f"Progress: {i}/{max_iterations} ({i/max_iterations*100:.1f}%) | "
                      f"Success: {successful_runs} ({success_rate:.1f}%) | "
                      f"Failed: {failed_runs} | "
                      f"Best Score: {best_score:.4f}")
                
                if best_params:
                    print(f"  Best Params: WS={best_params['window_size']}, "
                          f"LD={best_params['n_liquidity_days']}, "
                          f"LA={best_params['n_liquidity_assets']}, "
                          f"SA={best_params['n_selected_assets']}")
                print()
        
        print(f"Optimization completed!")
        print(f"Total iterations: {max_iterations}")
        print(f"Successful runs: {successful_runs}")
        print(f"Failed runs: {failed_runs}")
        print(f"Success rate: {(successful_runs/max_iterations)*100:.1f}%")
        print(f"Best risk-adjusted score: {best_score:.4f}")
        if best_params:
            print(f"Best parameters: {best_params}")
        print(f"Results saved to: {self.results_file}")
    
    def analyze_results(self, min_trades=50):
        """Analyze the saved results."""
        if not os.path.exists(self.results_file):
            print(f"Results file {self.results_file} not found!")
            return None
        
        # Load results
        df = pd.read_csv(self.results_file)
        
        if df.empty:
            print("No results found in file!")
            return None
        
        print(f"Analyzing {len(df)} optimization results...")
        
        # Filter by minimum trades
        valid_df = df[df['total_trades'] >= min_trades].copy()
        
        if valid_df.empty:
            print(f"No results with minimum {min_trades} trades found!")
            print(f"Maximum trades found: {df['total_trades'].max()}")
            return df
        
        print(f"Valid results (>= {min_trades} trades): {len(valid_df)}")
        
        # Sort by risk-adjusted score
        valid_df = valid_df.sort_values('risk_adjusted_score', ascending=False)
        
        # Show top 10 results
        print(f"\nTop 10 Results:")
        print("-" * 80)
        print(f"{'Rank':<4} {'WS':<3} {'LD':<4} {'LA':<4} {'SA':<3} {'Score':<8} {'Sharpe':<8} {'Trades':<6} {'Return':<8}")
        print("-" * 80)
        
        for i, (_, row) in enumerate(valid_df.head(10).iterrows(), 1):
            print(f"{i:<4} {int(row['window_size']):<3} {int(row['n_liquidity_days']):<4} "
                  f"{int(row['n_liquidity_assets']):<4} {int(row['n_selected_assets']):<3} "
                  f"{row['risk_adjusted_score']:<8.4f} {row['sharpe_ratio']:<8.3f} "
                  f"{int(row['total_trades']):<6} {row['annual_return']:<8.2%}")
        
        # Statistics
        print(f"\nStatistics:")
        print(f"  Best Score: {valid_df['risk_adjusted_score'].max():.4f}")
        print(f"  Average Score: {valid_df['risk_adjusted_score'].mean():.4f}")
        print(f"  Best Sharpe: {valid_df['sharpe_ratio'].max():.3f}")
        print(f"  Average Sharpe: {valid_df['sharpe_ratio'].mean():.3f}")
        print(f"  Best Annual Return: {valid_df['annual_return'].max():.2%}")
        print(f"  Average Annual Return: {valid_df['annual_return'].mean():.2%}")
        print(f"  Lowest Max Drawdown: {valid_df['max_drawdown'].min():.2%}")
        print(f"  Average Max Drawdown: {valid_df['max_drawdown'].mean():.2%}")
        
        # Parameter analysis
        print(f"\nParameter Ranges in Top 10%:")
        top_10_pct = valid_df.head(max(10, len(valid_df) // 10))
        print(f"  Window Size: {top_10_pct['window_size'].min()}-{top_10_pct['window_size'].max()}")
        print(f"  Liquidity Days: {top_10_pct['n_liquidity_days'].min()}-{top_10_pct['n_liquidity_days'].max()}")
        print(f"  Liquidity Assets: {top_10_pct['n_liquidity_assets'].min()}-{top_10_pct['n_liquidity_assets'].max()}")
        print(f"  Selected Assets: {top_10_pct['n_selected_assets'].min()}-{top_10_pct['n_selected_assets'].max()}")
        
        return valid_df
    
    def get_best_parameters(self, min_trades=50):
        """Get the best parameters from saved results."""
        df = self.analyze_results(min_trades)
        if df is not None and not df.empty:
            best = df.iloc[0]
            return {
                'window_size': int(best['window_size']),
                'n_liquidity_days': int(best['n_liquidity_days']),
                'n_liquidity_assets': int(best['n_liquidity_assets']),
                'n_selected_assets': int(best['n_selected_assets'])
            }
        return None


def main():
    """Main function to run random optimization."""
    print("="*80)
    print("RANDOM PARAMETER OPTIMIZATION")
    print("="*80)
    print("Parameter Ranges:")
    print("  window_size: 1-252")
    print("  n_liquidity_days: 5-252")
    print("  n_liquidity_assets: 20-300")
    print("  n_selected_assets: 1-20")
    print("="*80)
    print()
    
    # Initialize optimizer
    optimizer = RandomOptimizer()
    
    # Test current parameters first
    print("Testing baseline parameters...")
    current_params = {
        'window_size': 10,
        'n_liquidity_days': 90,
        'n_liquidity_assets': 50,
        'n_selected_assets': 10
    }
    
    trades, metrics = optimizer.optimizer.run_backtest(current_params)
    print(f"Baseline Performance:")
    print(f"  Risk-Adjusted Score: {metrics['risk_adjusted_score']:.4f}")
    print(f"  Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
    print(f"  Annual Return: {metrics['annual_return']:.2%}")
    print(f"  Max Drawdown: {metrics['max_drawdown']:.2%}")
    print(f"  Total Trades: {metrics['total_trades']}")
    print()
    
    # Ask user for number of iterations
    try:
        max_iterations = int(input("Enter number of random iterations to run (default 500): ") or "500")
    except ValueError:
        max_iterations = 500
    
    print(f"Running {max_iterations} random optimizations...")
    print("Results will be saved after each iteration.")
    print("You can stop the process anytime with Ctrl+C and analyze partial results.")
    print()
    
    try:
        # Run optimization
        optimizer.run_optimization(max_iterations=max_iterations, progress_interval=25)
        
        # Analyze results
        print("\nAnalyzing final results...")
        best_params = optimizer.get_best_parameters()
        
        if best_params:
            print(f"\nBest parameters found:")
            for param, value in best_params.items():
                print(f"  {param}: {value}")
            
            # Compare with baseline
            print(f"\nComparing best vs baseline...")
            best_trades, best_metrics = optimizer.optimizer.run_backtest(best_params)
            improvement = ((best_metrics['risk_adjusted_score'] / metrics['risk_adjusted_score']) - 1) * 100
            
            print(f"Best Performance:")
            print(f"  Risk-Adjusted Score: {best_metrics['risk_adjusted_score']:.4f}")
            print(f"  Improvement: {improvement:.1f}%")
            
            if improvement > 5:
                print("🎯 SIGNIFICANT IMPROVEMENT FOUND!")
            elif improvement > 0:
                print("✅ Minor improvement found")
            else:
                print("❌ No improvement over baseline")
        
    except KeyboardInterrupt:
        print("\n\nOptimization interrupted by user.")
        print("Analyzing partial results...")
        optimizer.analyze_results()
        print(f"Results saved to: {optimizer.results_file}")
        print("You can resume optimization later or analyze current results.")


if __name__ == "__main__":
    main()
