"""
Parameter Optimizer Module

This module provides comprehensive parameter optimization for the trading system
using grid search, walk-forward analysis, and statistical validation.
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Callable
import warnings
from concurrent.futures import <PERSON><PERSON>oolExecutor, as_completed
from tqdm import tqdm
import json
import os

from financial_metrics import FinancialMetrics
from optimization_config import OptimizationConfig

warnings.filterwarnings('ignore')


class ParameterOptimizer:
    """
    Comprehensive parameter optimizer for trading system.

    Features:
    - Grid search optimization
    - Walk-forward analysis
    - Statistical significance testing
    - Risk-adjusted performance evaluation
    - Parallel processing for efficiency
    """

    def __init__(self, db_name: str = 'cotacoes copy.db',
                 total_value: float = 25000, value_per_asset: float = 5000,
                 offer_spread: float = 0.1, risk_free_rate: float = 0.1, max_window_size: int =252):
        """
        Initialize the parameter optimizer.

        Args:
            db_name: Database name for market data
            total_value: Total capital available
            value_per_asset: Capital per asset position
            offer_spread: Bid-ask spread assumption
            risk_free_rate: Risk-free rate for calculations
        """
        self.db_name = db_name
        self.total_value = total_value
        self.value_per_asset = value_per_asset
        self.offer_spread = offer_spread
        self.risk_free_rate = risk_free_rate
        self.max_window_size = max_window_size

        self.metrics_calculator = FinancialMetrics(risk_free_rate)
        self.config = OptimizationConfig()

        # Results storage
        self.optimization_results = []
        self.best_parameters = None
        self.performance_summary = None

    def get_trade_system_data(self, window_size: int = 10, n_liquidity_days: int = 90,
                             n_liquidity_assets: int = 50, n_selected_assets: int = 10) -> pd.DataFrame:
        """
        Retrieve trade system data.

        Args:
            window_size: Moving sum window size
            n_liquidity_days: Days for liquidity calculation
            n_liquidity_assets: Number of liquid assets to consider
            n_selected_assets: Number of assets to select for trading

        Returns:
            DataFrame with trading system data
        """
        conn = sqlite3.connect(self.db_name)

        query = f"""
        WITH liquidity_ranked AS (
            SELECT
                c_base.date,
                c_base.symbol,
                SUM(c_window.close * c_window.volume) AS total_financial_volume_rolling_days,
                ROW_NUMBER() OVER (PARTITION BY c_base.date ORDER BY SUM(c_window.close * c_window.volume) DESC) AS rn
            FROM
                cotacoes AS c_base
            JOIN
                cotacoes AS c_window ON c_window.symbol = c_base.symbol
                AND c_window.date BETWEEN DATE(c_base.date, '-' || ({n_liquidity_days} - 1) || ' day') AND c_base.date
            WHERE
                c_base.close * c_base.volume IS NOT NULL
                AND c_window.close * c_window.volume IS NOT NULL
            GROUP BY
                c_base.date, c_base.symbol
        ),
        thresholds AS (
            SELECT
                date,
                MIN(total_financial_volume_rolling_days) AS liquidity_volume_threshold
            FROM
                liquidity_ranked
            WHERE
                rn = {n_liquidity_assets}
            GROUP BY
                date
        ),
        asset_volumes AS (
            SELECT
                c.date,
                c.symbol,
                SUM(cw.close * cw.volume) AS total_financial_volume_rolling_days
            FROM
                cotacoes AS c
            JOIN
                cotacoes AS cw ON cw.symbol = c.symbol
                AND cw.date BETWEEN DATE(c.date, '-' || ({n_liquidity_days} - 1) || ' day') AND c.date
            WHERE
                c.close * c.volume IS NOT NULL
                AND cw.close * cw.volume IS NOT NULL
            GROUP BY
                c.date, c.symbol
        ),
        assets_above_threshold AS (
            SELECT
                a.date,
                a.symbol
            FROM
                asset_volumes AS a
            JOIN
                thresholds AS t ON a.date = t.date
            WHERE
                a.total_financial_volume_rolling_days >= t.liquidity_volume_threshold
        ),
        prices_with_lags AS (
            SELECT
                c.symbol,
                c.date,
                c.open,
                c.close,
                LAG(c.close, 1) OVER (PARTITION BY c.symbol ORDER BY c.date) AS close_d_minus_1,
                LAG(c.close, 2) OVER (PARTITION BY c.symbol ORDER BY c.date) AS close_d_minus_2
            FROM
                cotacoes AS c
        ),
        signals AS (
            SELECT
                p.symbol,
                p.date,
                p.open,
                p.close,
                p.close_d_minus_1,
                p.close_d_minus_2,
                CASE
                    WHEN p.open > p.close_d_minus_1 THEN 1
                    WHEN p.open < p.close_d_minus_1 THEN -1
                    ELSE 0
                END AS signal
            FROM
                prices_with_lags AS p
        ),
        moving_sums AS (
            SELECT
                s.*,
                SUM(signal) OVER (
                    PARTITION BY s.symbol
                    ORDER BY s.date
                    ROWS BETWEEN {window_size - 1} PRECEDING AND CURRENT ROW
                ) AS moving_sum
            FROM
                signals AS s
        ),
        selected_assets AS (
            SELECT
                m.date,
                m.symbol,
                m.close_d_minus_2,
                m.close_d_minus_1,
                m.open AS open_d_plus_0,
                m.moving_sum,
                ROW_NUMBER() OVER (PARTITION BY m.date ORDER BY m.moving_sum DESC) AS rn
            FROM
                moving_sums AS m
            JOIN
                assets_above_threshold AS a ON m.symbol = a.symbol AND m.date = a.date
        ),
        top_assets AS (
            SELECT
                *
            FROM
                selected_assets
            WHERE
                rn <= {n_selected_assets}
        )
        SELECT
            date,
            symbol,
            close_d_minus_2,
            close_d_minus_1,
            open_d_plus_0,
            moving_sum
        FROM
            top_assets
        ORDER BY
            date, symbol;
        """

        # Execute query and load results into a DataFrame
        df = pd.read_sql_query(query, conn, parse_dates=['date'])

        # Close DB connection
        conn.close()

        df_sorted = df.sort_values(by=["moving_sum", "date"], ascending=[False, True]).copy()

        # Count distinct dates
        distinct_dates = df_sorted['date'].drop_duplicates().reset_index(drop=True)

        # Get the date at position `max_window_size`
        if self.max_window_size >= len(distinct_dates):
            raise ValueError("max_window_size is larger than the number of distinct dates.")
        start_date = distinct_dates.iloc[self.max_window_size]

        # Filter the DataFrame to consider only dates above this date
        df_filtered = df_sorted[df_sorted['date'] >= start_date].copy()

        return df_filtered

    def get_open_price_from_db(self, symbol: str, date: str) -> float:
        """Get opening price for a symbol on a specific date."""
        conn = sqlite3.connect(self.db_name)
        query = f"""
            SELECT open
            FROM cotacoes
            WHERE symbol = '{symbol}' AND date = '{date}'
            LIMIT 1
        """
        df = pd.read_sql_query(query, conn)
        conn.close()

        if not df.empty:
            return df.loc[0, 'open']
        else:
            return np.nan

    def run_backtest(self, params: Dict) -> Tuple[Dict, Dict[str, float]]:
        """
        Run backtest with given parameters.

        Args:
            params: Parameter dictionary

        Returns:
            Tuple of (trades_dict, metrics_dict)
        """
        try:
            # Get data for the specified period
            df = self.get_trade_system_data(
                window_size=params['window_size'],
                n_liquidity_days=params['n_liquidity_days'],
                n_liquidity_assets=params['n_liquidity_assets'],
                n_selected_assets=params['n_selected_assets'],
            )

            if df.empty:
                return {}, self._get_empty_metrics()

            # Run trading simulation
            available_capital = self.total_value
            open_positions = {}
            trades = {}

            for date, daily_assets in df.groupby('date'):
                # Process new positions
                for idx, asset in daily_assets.iterrows():
                    if asset['symbol'] in open_positions or available_capital <= self.value_per_asset:
                        continue

                    if np.isnan(asset['close_d_minus_2']):
                        continue

                    asset_quantity = int(self.value_per_asset / (asset['close_d_minus_2'] * 100)) * 100
                    if asset_quantity == 0:
                        continue

                    order_value = asset['close_d_minus_2'] * (1 + self.offer_spread)
                    if order_value >= asset['close_d_minus_1']:
                        open_positions[asset['symbol']] = {
                            'quantity': asset_quantity,
                            'buy_price': asset['close_d_minus_1'],
                            'sell_price': asset['open_d_plus_0'],
                            'date': date
                        }
                        available_capital -= asset_quantity * asset['close_d_minus_1']

                # Process position closures
                for symbol, position in open_positions.copy().items():
                    sell_price = position['sell_price']

                    if np.isnan(sell_price) and date == position['date']:
                        continue
                    elif np.isnan(sell_price):
                        sell_price = self.get_open_price_from_db(symbol, str(date.date()))
                        if np.isnan(sell_price):
                            continue

                    available_capital += position['quantity'] * sell_price
                    trades[(symbol, position['date'])] = {
                        'symbol': symbol,
                        'quantity': position['quantity'],
                        'buy_price': position['buy_price'],
                        'sell_price': sell_price,
                        'profit': (sell_price - position['buy_price']) * position['quantity']
                    }
                    open_positions.pop(symbol)

            # Calculate metrics
            if not trades:
                return trades, self._get_empty_metrics()

            # Calculate total trading days
            total_days = len(df['date'].unique())

            # Calculate comprehensive metrics
            metrics = self.metrics_calculator.calculate_comprehensive_metrics(
                trades, self.total_value, total_days
            )

            # Add risk-adjusted score
            metrics['risk_adjusted_score'] = self.metrics_calculator.risk_adjusted_score(metrics)

            return trades, metrics

        except Exception as e:
            print(f"Error in backtest with params {params}: {str(e)}")
            return {}, self._get_empty_metrics()

    def _get_empty_metrics(self) -> Dict[str, float]:
        """Return empty metrics dictionary for failed backtests."""
        return {
            'total_return': 0.0,
            'annual_return': 0.0,
            'sharpe_ratio': 0.0,
            'sortino_ratio': 0.0,
            'max_drawdown': 1.0,  # Worst possible drawdown
            'max_drawdown_duration': 0,
            'calmar_ratio': 0.0,
            'var_5': 0.0,
            'cvar_5': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'avg_trade_return': 0.0,
            'total_trades': 0,
            'trades_per_day': 0.0,
            'volatility': 0.0,
            'skewness': 0.0,
            'kurtosis': 0.0,
            'risk_adjusted_score': 0.0
        }

    def optimize_parameters(self, optimization_type: str = 'coarse',
                          n_jobs: int = 4, save_results: bool = True) -> pd.DataFrame:
        """
        Optimize parameters using grid search.

        Args:
            optimization_type: Type of optimization ('full', 'coarse', 'fine', 'focused')
            n_jobs: Number of parallel jobs
            save_results: Whether to save results to file

        Returns:
            DataFrame with optimization results
        """
        print(f"Starting {optimization_type} parameter optimization...")

        # Get parameter combinations
        param_combinations = self.config.get_parameter_grid(optimization_type)
        print(f"Testing {len(param_combinations)} parameter combinations...")

        # Validate parameters
        valid_combinations = []
        for params in param_combinations:
            is_valid, error_msg = self.config.validate_parameters(params)
            if is_valid:
                valid_combinations.append(params)
            else:
                print(f"Skipping invalid parameters {params}: {error_msg}")

        print(f"Running optimization on {len(valid_combinations)} valid combinations...")

        # Run optimization with parallel processing
        results = []

        if n_jobs == 1:
            # Sequential processing for debugging
            for params in tqdm(valid_combinations, desc="Optimizing"):
                trades, metrics = self.run_backtest(params)
                result = {**params, **metrics}
                results.append(result)
        else:
            # Parallel processing
            with ProcessPoolExecutor(max_workers=n_jobs) as executor:
                # Submit all jobs
                future_to_params = {
                    executor.submit(self.run_backtest, params): params
                    for params in valid_combinations
                }

                # Collect results
                for future in tqdm(as_completed(future_to_params),
                                 total=len(valid_combinations), desc="Optimizing"):
                    params = future_to_params[future]
                    try:
                        trades, metrics = future.result()
                        result = {**params, **metrics}
                        results.append(result)
                    except Exception as e:
                        print(f"Error processing {params}: {str(e)}")
                        # Add failed result with zero metrics
                        result = {**params, **self._get_empty_metrics()}
                        results.append(result)

        # Convert to DataFrame
        results_df = pd.DataFrame(results)

        # Filter out results with insufficient trades
        min_trades = self.config.optimization_settings['min_trades_threshold']
        results_df = results_df[results_df['total_trades'] >= min_trades].copy()

        if results_df.empty:
            print("Warning: No parameter combinations met the minimum trade threshold!")
            results_df = pd.DataFrame(results)

        # Sort by risk-adjusted score
        results_df = results_df.sort_values('risk_adjusted_score', ascending=False)

        # Store results
        self.optimization_results = results_df

        # Identify best parameters
        if not results_df.empty:
            self.best_parameters = results_df.iloc[0][['window_size', 'n_liquidity_days',
                                                      'n_liquidity_assets', 'n_selected_assets']].to_dict()

            # Create performance summary
            self._create_performance_summary(results_df)

        # Save results if requested
        if save_results:
            self._save_optimization_results(results_df, optimization_type)

        print(f"Optimization complete! Best risk-adjusted score: {results_df.iloc[0]['risk_adjusted_score']:.4f}")
        print(f"Best parameters: {self.best_parameters}")

        return results_df

    def walk_forward_analysis(self, params: Dict, n_periods: int = 4) -> Dict:
        """
        Perform simplified walk-forward analysis for parameter validation.
        Uses the full dataset and splits it into periods.

        Args:
            params: Parameters to test
            n_periods: Number of walk-forward periods

        Returns:
            Dictionary with walk-forward results
        """
        print(f"Running walk-forward analysis with {n_periods} periods...")

        # Get full dataset
        df = self.get_trade_system_data(
            window_size=params['window_size'],
            n_liquidity_days=params['n_liquidity_days'],
            n_liquidity_assets=params['n_liquidity_assets'],
            n_selected_assets=params['n_selected_assets']
        )

        if df.empty:
            return {'error': 'No data available for walk-forward analysis'}

        # Split data into periods
        dates = sorted(df['date'].unique())
        total_dates = len(dates)
        period_size = total_dates // n_periods

        results = []

        for i in range(n_periods):
            start_idx = i * period_size
            end_idx = min((i + 1) * period_size, total_dates)

            period_dates = dates[start_idx:end_idx]
            period_df = df[df['date'].isin(period_dates)]

            if period_df.empty:
                continue

            print(f"Period {i+1}: {period_dates[0]} to {period_dates[-1]} ({len(period_dates)} days)")

            # Run backtest on this period
            trades, metrics = self._run_backtest_on_data(period_df)

            period_result = {
                'period': i + 1,
                'start_date': str(period_dates[0]),
                'end_date': str(period_dates[-1]),
                'days': len(period_dates),
                'trades_count': len(trades),
                **metrics
            }

            results.append(period_result)

        if not results:
            return {'error': 'No valid periods for analysis'}

        # Calculate summary statistics
        metrics_df = pd.DataFrame(results)

        summary = {
            'periods': results,
            'avg_return': metrics_df['total_return'].mean(),
            'std_return': metrics_df['total_return'].std(),
            'avg_sharpe': metrics_df['sharpe_ratio'].mean(),
            'std_sharpe': metrics_df['sharpe_ratio'].std(),
            'avg_max_dd': metrics_df['max_drawdown'].mean(),
            'worst_max_dd': metrics_df['max_drawdown'].max(),
            'consistency_score': (metrics_df['total_return'] > 0).mean(),
            'avg_trades_per_period': metrics_df['trades_count'].mean()
        }

        return summary

    def _run_backtest_on_data(self, df: pd.DataFrame) -> Tuple[Dict, Dict[str, float]]:
        """
        Run backtest on provided DataFrame.

        Args:
            df: DataFrame with trading system data

        Returns:
            Tuple of (trades_dict, metrics_dict)
        """
        if df.empty:
            return {}, self._get_empty_metrics()

        # Run trading simulation
        available_capital = self.total_value
        open_positions = {}
        trades = {}

        for date, daily_assets in df.groupby('date'):
            # Process new positions
            for idx, asset in daily_assets.iterrows():
                if asset['symbol'] in open_positions or available_capital <= self.value_per_asset:
                    continue

                if np.isnan(asset['close_d_minus_2']):
                    continue

                asset_quantity = int(self.value_per_asset / (asset['close_d_minus_2'] * 100)) * 100
                if asset_quantity == 0:
                    continue

                order_value = asset['close_d_minus_2'] * (1 + self.offer_spread)
                if order_value >= asset['close_d_minus_1']:
                    open_positions[asset['symbol']] = {
                        'quantity': asset_quantity,
                        'buy_price': asset['close_d_minus_1'],
                        'sell_price': asset['open_d_plus_0'],
                        'date': date
                    }
                    available_capital -= asset_quantity * asset['close_d_minus_1']

            # Process position closures
            for symbol, position in open_positions.copy().items():
                sell_price = position['sell_price']

                if np.isnan(sell_price) and date == position['date']:
                    continue
                elif np.isnan(sell_price):
                    sell_price = self.get_open_price_from_db(symbol, str(date.date()))
                    if np.isnan(sell_price):
                        continue

                available_capital += position['quantity'] * sell_price
                trades[(symbol, position['date'])] = {
                    'symbol': symbol,
                    'quantity': position['quantity'],
                    'buy_price': position['buy_price'],
                    'sell_price': sell_price,
                    'profit': (sell_price - position['buy_price']) * position['quantity']
                }
                open_positions.pop(symbol)

        # Calculate metrics
        if not trades:
            return trades, self._get_empty_metrics()

        # Calculate total trading days
        total_days = len(df['date'].unique())

        # Calculate comprehensive metrics
        metrics = self.metrics_calculator.calculate_comprehensive_metrics(
            trades, self.total_value, total_days
        )

        # Add risk-adjusted score
        metrics['risk_adjusted_score'] = self.metrics_calculator.risk_adjusted_score(metrics)

        return trades, metrics

    def _create_performance_summary(self, results_df: pd.DataFrame):
        """Create performance summary from optimization results."""
        if results_df.empty:
            return

        # Get top performers
        top_10 = results_df.head(10)

        # Performance categories
        thresholds = self.config.performance_thresholds

        excellent_count = 0
        good_count = 0
        acceptable_count = 0

        for _, row in results_df.iterrows():
            if (row['sharpe_ratio'] >= thresholds['excellent']['sharpe_ratio'] and
                row['max_drawdown'] <= thresholds['excellent']['max_drawdown']):
                excellent_count += 1
            elif (row['sharpe_ratio'] >= thresholds['good']['sharpe_ratio'] and
                  row['max_drawdown'] <= thresholds['good']['max_drawdown']):
                good_count += 1
            elif (row['sharpe_ratio'] >= thresholds['acceptable']['sharpe_ratio'] and
                  row['max_drawdown'] <= thresholds['acceptable']['max_drawdown']):
                acceptable_count += 1

        self.performance_summary = {
            'total_combinations': len(results_df),
            'excellent_performers': excellent_count,
            'good_performers': good_count,
            'acceptable_performers': acceptable_count,
            'best_risk_adjusted_score': results_df.iloc[0]['risk_adjusted_score'],
            'best_sharpe_ratio': results_df['sharpe_ratio'].max(),
            'best_calmar_ratio': results_df['calmar_ratio'].max(),
            'lowest_max_drawdown': results_df['max_drawdown'].min(),
            'highest_win_rate': results_df['win_rate'].max(),
            'top_10_avg_score': top_10['risk_adjusted_score'].mean(),
            'parameter_stability': {
                'window_size_range': (top_10['window_size'].min(), top_10['window_size'].max()),
                'liquidity_days_range': (top_10['n_liquidity_days'].min(), top_10['n_liquidity_days'].max()),
                'liquidity_assets_range': (top_10['n_liquidity_assets'].min(), top_10['n_liquidity_assets'].max()),
                'selected_assets_range': (top_10['n_selected_assets'].min(), top_10['n_selected_assets'].max())
            }
        }

    def _save_optimization_results(self, results_df: pd.DataFrame, optimization_type: str):
        """Save optimization results to files."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save detailed results
        results_file = f'optimization_results_{optimization_type}_{timestamp}.csv'
        results_df.to_csv(results_file, index=False)
        print(f"Detailed results saved to: {results_file}")

        # Save summary
        if self.performance_summary:
            summary_file = f'optimization_summary_{optimization_type}_{timestamp}.json'
            with open(summary_file, 'w') as f:
                json.dump(self.performance_summary, f, indent=2, default=str)
            print(f"Summary saved to: {summary_file}")

    def get_top_parameters(self, n: int = 10) -> pd.DataFrame:
        """
        Get top N parameter combinations.

        Args:
            n: Number of top combinations to return

        Returns:
            DataFrame with top parameter combinations
        """
        if self.optimization_results is None or self.optimization_results.empty:
            print("No optimization results available. Run optimization first.")
            return pd.DataFrame()

        return self.optimization_results.head(n)

    def analyze_parameter_sensitivity(self) -> Dict:
        """
        Analyze parameter sensitivity from optimization results.

        Returns:
            Dictionary with sensitivity analysis
        """
        if self.optimization_results is None or self.optimization_results.empty:
            print("No optimization results available. Run optimization first.")
            return {}

        df = self.optimization_results

        # Calculate correlations between parameters and performance
        param_cols = ['window_size', 'n_liquidity_days', 'n_liquidity_assets', 'n_selected_assets']
        performance_cols = ['risk_adjusted_score', 'sharpe_ratio', 'calmar_ratio', 'max_drawdown']

        correlations = {}
        for param in param_cols:
            correlations[param] = {}
            for perf in performance_cols:
                correlations[param][perf] = df[param].corr(df[perf])

        # Find optimal ranges for each parameter
        top_20_percent = df.head(int(len(df) * 0.2))

        optimal_ranges = {}
        for param in param_cols:
            optimal_ranges[param] = {
                'min': top_20_percent[param].min(),
                'max': top_20_percent[param].max(),
                'mean': top_20_percent[param].mean(),
                'std': top_20_percent[param].std()
            }

        return {
            'correlations': correlations,
            'optimal_ranges': optimal_ranges,
            'top_20_percent_stats': top_20_percent[param_cols + ['risk_adjusted_score']].describe()
        }
