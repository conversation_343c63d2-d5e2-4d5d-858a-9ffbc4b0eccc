"""
Optimization Results Analyzer

This script analyzes saved optimization results and provides comprehensive insights.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
import glob
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class OptimizationAnalyzer:
    """
    Analyzer for optimization results.
    """
    
    def __init__(self, results_file=None):
        """Initialize the analyzer."""
        if results_file is None:
            # Find the most recent results file
            results_files = glob.glob('random_optimization_results_*.csv')
            if not results_files:
                raise FileNotFoundError("No optimization results files found!")
            results_file = max(results_files, key=os.path.getctime)
            print(f"Using most recent results file: {results_file}")
        
        self.results_file = results_file
        self.df = None
        self.valid_df = None
        
        # Load data
        self.load_data()
    
    def load_data(self, min_trades=50):
        """Load and filter optimization results."""
        if not os.path.exists(self.results_file):
            raise FileNotFoundError(f"Results file {self.results_file} not found!")
        
        print(f"Loading results from: {self.results_file}")
        self.df = pd.read_csv(self.results_file)
        
        if self.df.empty:
            raise ValueError("No results found in file!")
        
        print(f"Total results loaded: {len(self.df)}")
        
        # Filter by minimum trades
        self.valid_df = self.df[self.df['total_trades'] >= min_trades].copy()
        
        if self.valid_df.empty:
            print(f"WARNING: No results with minimum {min_trades} trades!")
            print(f"Using all results. Max trades: {self.df['total_trades'].max()}")
            self.valid_df = self.df.copy()
        else:
            print(f"Valid results (>= {min_trades} trades): {len(self.valid_df)}")
        
        # Sort by risk-adjusted score
        self.valid_df = self.valid_df.sort_values('risk_adjusted_score', ascending=False)
    
    def get_summary_statistics(self):
        """Get summary statistics of the optimization results."""
        if self.valid_df is None or self.valid_df.empty:
            return None
        
        stats = {
            'total_iterations': len(self.df),
            'valid_results': len(self.valid_df),
            'success_rate': len(self.valid_df) / len(self.df) * 100,
            'best_score': self.valid_df['risk_adjusted_score'].max(),
            'avg_score': self.valid_df['risk_adjusted_score'].mean(),
            'std_score': self.valid_df['risk_adjusted_score'].std(),
            'best_sharpe': self.valid_df['sharpe_ratio'].max(),
            'avg_sharpe': self.valid_df['sharpe_ratio'].mean(),
            'best_return': self.valid_df['annual_return'].max(),
            'avg_return': self.valid_df['annual_return'].mean(),
            'min_drawdown': self.valid_df['max_drawdown'].min(),
            'avg_drawdown': self.valid_df['max_drawdown'].mean(),
            'max_trades': self.valid_df['total_trades'].max(),
            'avg_trades': self.valid_df['total_trades'].mean()
        }
        
        return stats
    
    def print_summary(self):
        """Print summary statistics."""
        stats = self.get_summary_statistics()
        if stats is None:
            print("No valid results to analyze!")
            return
        
        print("="*60)
        print("OPTIMIZATION RESULTS SUMMARY")
        print("="*60)
        print(f"Total Iterations: {stats['total_iterations']}")
        print(f"Valid Results: {stats['valid_results']}")
        print(f"Success Rate: {stats['success_rate']:.1f}%")
        print()
        print("Performance Metrics:")
        print(f"  Best Risk-Adjusted Score: {stats['best_score']:.4f}")
        print(f"  Average Score: {stats['avg_score']:.4f} (±{stats['std_score']:.4f})")
        print(f"  Best Sharpe Ratio: {stats['best_sharpe']:.3f}")
        print(f"  Average Sharpe: {stats['avg_sharpe']:.3f}")
        print(f"  Best Annual Return: {stats['best_return']:.2%}")
        print(f"  Average Return: {stats['avg_return']:.2%}")
        print(f"  Lowest Max Drawdown: {stats['min_drawdown']:.2%}")
        print(f"  Average Drawdown: {stats['avg_drawdown']:.2%}")
        print(f"  Max Trades: {int(stats['max_trades'])}")
        print(f"  Average Trades: {stats['avg_trades']:.0f}")
    
    def get_top_performers(self, n=20):
        """Get top N performing parameter combinations."""
        if self.valid_df is None or self.valid_df.empty:
            return None
        
        top_n = self.valid_df.head(n)
        
        print(f"\nTOP {n} PARAMETER COMBINATIONS")
        print("="*80)
        print(f"{'Rank':<4} {'WS':<3} {'LD':<4} {'LA':<4} {'SA':<3} {'Score':<8} {'Sharpe':<8} {'Return':<8} {'DD':<6} {'Trades':<6}")
        print("="*80)
        
        for i, (_, row) in enumerate(top_n.iterrows(), 1):
            print(f"{i:<4} {int(row['window_size']):<3} {int(row['n_liquidity_days']):<4} "
                  f"{int(row['n_liquidity_assets']):<4} {int(row['n_selected_assets']):<3} "
                  f"{row['risk_adjusted_score']:<8.4f} {row['sharpe_ratio']:<8.3f} "
                  f"{row['annual_return']:<8.2%} {row['max_drawdown']:<6.2%} {int(row['total_trades']):<6}")
        
        return top_n
    
    def analyze_parameter_patterns(self):
        """Analyze parameter patterns in top performers."""
        if self.valid_df is None or self.valid_df.empty:
            return
        
        # Top 10% performers
        top_10_pct = self.valid_df.head(max(10, len(self.valid_df) // 10))
        
        print(f"\nPARAMETER ANALYSIS (Top {len(top_10_pct)} performers)")
        print("="*50)
        
        param_cols = ['window_size', 'n_liquidity_days', 'n_liquidity_assets', 'n_selected_assets']
        
        for param in param_cols:
            values = top_10_pct[param]
            print(f"{param}:")
            print(f"  Range: {values.min()}-{values.max()}")
            print(f"  Mean: {values.mean():.1f}")
            print(f"  Median: {values.median():.1f}")
            print(f"  Std: {values.std():.1f}")
            
            # Most common values
            value_counts = values.value_counts().head(3)
            print(f"  Most common: {dict(value_counts)}")
            print()
        
        # Correlations
        print("Parameter Correlations with Performance:")
        performance_cols = ['risk_adjusted_score', 'sharpe_ratio', 'annual_return', 'max_drawdown']
        
        for perf_metric in performance_cols:
            print(f"\n{perf_metric}:")
            for param in param_cols:
                corr = self.valid_df[param].corr(self.valid_df[perf_metric])
                print(f"  {param}: {corr:.3f}")
    
    def find_parameter_sweet_spots(self):
        """Find parameter sweet spots based on performance."""
        if self.valid_df is None or self.valid_df.empty:
            return
        
        print(f"\nPARAMETER SWEET SPOTS")
        print("="*40)
        
        # Define performance thresholds
        score_threshold = self.valid_df['risk_adjusted_score'].quantile(0.9)  # Top 10%
        
        sweet_spot_df = self.valid_df[self.valid_df['risk_adjusted_score'] >= score_threshold]
        
        if sweet_spot_df.empty:
            print("No clear sweet spots found!")
            return
        
        param_cols = ['window_size', 'n_liquidity_days', 'n_liquidity_assets', 'n_selected_assets']
        
        print(f"Based on top 10% performers (score >= {score_threshold:.4f}):")
        print(f"Number of combinations: {len(sweet_spot_df)}")
        print()
        
        for param in param_cols:
            values = sweet_spot_df[param]
            q25, q75 = values.quantile([0.25, 0.75])
            print(f"{param} sweet spot: {q25:.0f} - {q75:.0f}")
            print(f"  Most frequent: {values.mode().iloc[0] if not values.mode().empty else 'N/A'}")
            print()
    
    def compare_with_baseline(self, baseline_params=None):
        """Compare optimization results with baseline parameters."""
        if baseline_params is None:
            baseline_params = {
                'window_size': 10,
                'n_liquidity_days': 90,
                'n_liquidity_assets': 50,
                'n_selected_assets': 10
            }
        
        if self.valid_df is None or self.valid_df.empty:
            return
        
        # Find baseline in results (if tested)
        baseline_mask = (
            (self.valid_df['window_size'] == baseline_params['window_size']) &
            (self.valid_df['n_liquidity_days'] == baseline_params['n_liquidity_days']) &
            (self.valid_df['n_liquidity_assets'] == baseline_params['n_liquidity_assets']) &
            (self.valid_df['n_selected_assets'] == baseline_params['n_selected_assets'])
        )
        
        baseline_result = self.valid_df[baseline_mask]
        
        print(f"\nBASELINE COMPARISON")
        print("="*40)
        print(f"Baseline parameters: {baseline_params}")
        
        if not baseline_result.empty:
            baseline_score = baseline_result.iloc[0]['risk_adjusted_score']
            baseline_rank = (self.valid_df['risk_adjusted_score'] > baseline_score).sum() + 1
            print(f"Baseline score: {baseline_score:.4f}")
            print(f"Baseline rank: {baseline_rank}/{len(self.valid_df)} ({baseline_rank/len(self.valid_df)*100:.1f}%)")
        else:
            print("Baseline parameters not found in optimization results")
        
        # Best vs baseline
        best_result = self.valid_df.iloc[0]
        best_score = best_result['risk_adjusted_score']
        
        if not baseline_result.empty:
            improvement = ((best_score / baseline_score) - 1) * 100
            print(f"\nBest optimization result:")
            print(f"  Score: {best_score:.4f}")
            print(f"  Improvement: {improvement:.1f}%")
            
            if improvement > 10:
                print("🎯 SIGNIFICANT IMPROVEMENT FOUND!")
            elif improvement > 5:
                print("✅ GOOD IMPROVEMENT FOUND!")
            elif improvement > 0:
                print("⚠️  MINOR IMPROVEMENT FOUND")
            else:
                print("❌ NO IMPROVEMENT FOUND")
        else:
            print(f"\nBest optimization result:")
            print(f"  Score: {best_score:.4f}")
            print(f"  Parameters: WS={int(best_result['window_size'])}, "
                  f"LD={int(best_result['n_liquidity_days'])}, "
                  f"LA={int(best_result['n_liquidity_assets'])}, "
                  f"SA={int(best_result['n_selected_assets'])}")
    
    def create_visualizations(self):
        """Create comprehensive visualizations."""
        if self.valid_df is None or self.valid_df.empty:
            print("No valid data for visualizations!")
            return
        
        # Create figure with subplots
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        fig.suptitle('Random Optimization Results Analysis', fontsize=16, fontweight='bold')
        
        # Plot 1: Score distribution
        ax1 = axes[0, 0]
        ax1.hist(self.valid_df['risk_adjusted_score'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(self.valid_df['risk_adjusted_score'].mean(), color='red', linestyle='--', 
                    label=f'Mean: {self.valid_df["risk_adjusted_score"].mean():.3f}')
        ax1.set_xlabel('Risk-Adjusted Score')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Score Distribution')
        ax1.legend()
        
        # Plot 2: Risk-Return scatter
        ax2 = axes[0, 1]
        scatter = ax2.scatter(self.valid_df['max_drawdown'], self.valid_df['annual_return'], 
                             c=self.valid_df['risk_adjusted_score'], cmap='viridis', alpha=0.6)
        ax2.set_xlabel('Max Drawdown')
        ax2.set_ylabel('Annual Return')
        ax2.set_title('Risk-Return Profile')
        plt.colorbar(scatter, ax=ax2, label='Risk-Adjusted Score')
        
        # Plot 3: Sharpe vs Score
        ax3 = axes[0, 2]
        ax3.scatter(self.valid_df['sharpe_ratio'], self.valid_df['risk_adjusted_score'], 
                   c=self.valid_df['total_trades'], cmap='plasma', alpha=0.6)
        ax3.set_xlabel('Sharpe Ratio')
        ax3.set_ylabel('Risk-Adjusted Score')
        ax3.set_title('Sharpe vs Score')
        
        # Plot 4-7: Parameter distributions
        param_cols = ['window_size', 'n_liquidity_days', 'n_liquidity_assets', 'n_selected_assets']
        param_titles = ['Window Size', 'Liquidity Days', 'Liquidity Assets', 'Selected Assets']
        
        for i, (param, title) in enumerate(zip(param_cols, param_titles)):
            ax = axes[1, i] if i < 3 else axes[2, i-3]
            ax.hist(self.valid_df[param], bins=20, alpha=0.7, edgecolor='black')
            ax.set_xlabel(title)
            ax.set_ylabel('Frequency')
            ax.set_title(f'{title} Distribution')
        
        # Plot 8: Parameter correlation heatmap
        ax8 = axes[2, 1]
        corr_data = self.valid_df[param_cols + ['risk_adjusted_score']].corr()
        sns.heatmap(corr_data, annot=True, cmap='coolwarm', center=0, ax=ax8)
        ax8.set_title('Parameter Correlations')
        
        # Plot 9: Top performers over time
        ax9 = axes[2, 2]
        if 'iteration' in self.valid_df.columns:
            # Rolling maximum score
            rolling_max = self.valid_df.set_index('iteration')['risk_adjusted_score'].cummax()
            ax9.plot(rolling_max.index, rolling_max.values)
            ax9.set_xlabel('Iteration')
            ax9.set_ylabel('Best Score So Far')
            ax9.set_title('Optimization Progress')
        else:
            ax9.text(0.5, 0.5, 'No iteration data', ha='center', va='center', transform=ax9.transAxes)
            ax9.set_title('Optimization Progress')
        
        plt.tight_layout()
        
        # Save plot
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        plot_file = f'optimization_analysis_{timestamp}.png'
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"Visualizations saved to: {plot_file}")
    
    def export_best_parameters(self, n=10, filename=None):
        """Export best parameters to file."""
        if self.valid_df is None or self.valid_df.empty:
            return
        
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'best_parameters_{timestamp}.csv'
        
        # Get top N parameters
        top_n = self.valid_df.head(n)
        
        # Select relevant columns
        export_cols = ['window_size', 'n_liquidity_days', 'n_liquidity_assets', 'n_selected_assets',
                      'risk_adjusted_score', 'sharpe_ratio', 'annual_return', 'max_drawdown', 
                      'win_rate', 'total_trades']
        
        export_df = top_n[export_cols].copy()
        export_df.to_csv(filename, index=False)
        
        print(f"Top {n} parameters exported to: {filename}")


def main():
    """Main function to analyze optimization results."""
    print("="*60)
    print("OPTIMIZATION RESULTS ANALYZER")
    print("="*60)
    
    try:
        # Initialize analyzer
        analyzer = OptimizationAnalyzer()
        
        # Print summary
        analyzer.print_summary()
        
        # Show top performers
        analyzer.get_top_performers(20)
        
        # Analyze parameter patterns
        analyzer.analyze_parameter_patterns()
        
        # Find sweet spots
        analyzer.find_parameter_sweet_spots()
        
        # Compare with baseline
        analyzer.compare_with_baseline()
        
        # Create visualizations
        print(f"\nCreating visualizations...")
        analyzer.create_visualizations()
        
        # Export best parameters
        analyzer.export_best_parameters(10)
        
        print(f"\nAnalysis complete!")
        
    except Exception as e:
        print(f"Error during analysis: {str(e)}")
        print("Make sure you have run the random optimization first!")


if __name__ == "__main__":
    main()
