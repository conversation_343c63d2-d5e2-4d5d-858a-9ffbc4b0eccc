"""
Optimization Configuration Module

This module defines parameter ranges and optimization settings based on financial
theory and market microstructure principles for the trading system optimization.
"""

from typing import Dict, List, Tuple
import numpy as np


class OptimizationConfig:
    """
    Configuration class for trading system parameter optimization.
    
    Parameter ranges are based on financial theory and empirical research:
    
    1. Window Size (5-50): 
       - Short-term (5-15): Captures immediate momentum/reversal patterns
       - Medium-term (15-30): Balances noise reduction with responsiveness
       - Long-term (30-50): Reduces noise but may lag trend changes
    
    2. Liquidity Days (30-180):
       - 30-60 days: Recent liquidity patterns, more responsive to market changes
       - 60-120 days: Balanced approach, captures seasonal patterns
       - 120-180 days: Stable liquidity assessment, less sensitive to temporary shocks
    
    3. Liquidity Assets (20-200):
       - 20-50: Focus on most liquid assets, lower diversification
       - 50-100: Balanced liquidity and diversification
       - 100-200: Higher diversification, may include less liquid assets
    
    4. Selected Assets (5-25):
       - 5-10: Concentrated portfolio, higher individual asset impact
       - 10-15: Balanced concentration and diversification
       - 15-25: Higher diversification, lower individual asset impact
    """
    
    def __init__(self):
        """Initialize optimization configuration with parameter ranges."""
        
        # Define parameter ranges based on financial theory
        self.parameter_ranges = {
            'window_size': {
                'min': 5,
                'max': 50,
                'step': 5,
                'description': 'Moving sum window for signal calculation',
                'financial_rationale': 'Balances signal responsiveness vs noise reduction'
            },
            'n_liquidity_days': {
                'min': 30,
                'max': 180,
                'step': 15,
                'description': 'Days for liquidity calculation',
                'financial_rationale': 'Captures liquidity patterns while avoiding overfitting'
            },
            'n_liquidity_assets': {
                'min': 20,
                'max': 200,
                'step': 20,
                'description': 'Number of liquid assets to consider',
                'financial_rationale': 'Balances liquidity requirements with universe size'
            },
            'n_selected_assets': {
                'min': 5,
                'max': 25,
                'step': 5,
                'description': 'Number of assets to trade daily',
                'financial_rationale': 'Optimizes diversification vs concentration trade-off'
            }
        }
        
        # Optimization settings
        self.optimization_settings = {
            'objective_function': 'risk_adjusted_score',  # Primary optimization target
            'secondary_metrics': ['sharpe_ratio', 'calmar_ratio', 'max_drawdown'],
            'min_trades_threshold': 50,  # Minimum trades for valid backtest
            'max_drawdown_threshold': 0.30,  # Maximum acceptable drawdown (30%)
            'min_sharpe_threshold': 0.5,  # Minimum acceptable Sharpe ratio
            'walk_forward_periods': 4,  # Number of out-of-sample periods
            'train_test_ratio': 0.75,  # 75% training, 25% testing
        }
        
        # Risk management constraints
        self.risk_constraints = {
            'max_position_size': 0.20,  # Maximum 20% in single position
            'max_sector_exposure': 0.40,  # Maximum 40% in single sector
            'min_liquidity_volume': 1000000,  # Minimum daily volume (R$)
            'max_correlation': 0.70,  # Maximum correlation between selected assets
        }
        
        # Performance thresholds for parameter filtering
        self.performance_thresholds = {
            'excellent': {
                'sharpe_ratio': 1.5,
                'calmar_ratio': 1.0,
                'max_drawdown': 0.15,
                'win_rate': 60.0,
                'profit_factor': 1.5
            },
            'good': {
                'sharpe_ratio': 1.0,
                'calmar_ratio': 0.5,
                'max_drawdown': 0.20,
                'win_rate': 55.0,
                'profit_factor': 1.3
            },
            'acceptable': {
                'sharpe_ratio': 0.5,
                'calmar_ratio': 0.3,
                'max_drawdown': 0.25,
                'win_rate': 50.0,
                'profit_factor': 1.1
            }
        }
    
    def get_parameter_grid(self, optimization_type: str = 'full') -> List[Dict]:
        """
        Generate parameter combinations for optimization.
        
        Args:
            optimization_type: Type of optimization ('full', 'coarse', 'fine', 'focused')
            
        Returns:
            List of parameter dictionaries
        """
        if optimization_type == 'full':
            return self._generate_full_grid()
        elif optimization_type == 'coarse':
            return self._generate_coarse_grid()
        elif optimization_type == 'fine':
            return self._generate_fine_grid()
        elif optimization_type == 'focused':
            return self._generate_focused_grid()
        else:
            raise ValueError(f"Unknown optimization type: {optimization_type}")
    
    def _generate_full_grid(self) -> List[Dict]:
        """Generate full parameter grid for comprehensive optimization."""
        combinations = []
        
        for window_size in range(
            self.parameter_ranges['window_size']['min'],
            self.parameter_ranges['window_size']['max'] + 1,
            self.parameter_ranges['window_size']['step']
        ):
            for n_liquidity_days in range(
                self.parameter_ranges['n_liquidity_days']['min'],
                self.parameter_ranges['n_liquidity_days']['max'] + 1,
                self.parameter_ranges['n_liquidity_days']['step']
            ):
                for n_liquidity_assets in range(
                    self.parameter_ranges['n_liquidity_assets']['min'],
                    self.parameter_ranges['n_liquidity_assets']['max'] + 1,
                    self.parameter_ranges['n_liquidity_assets']['step']
                ):
                    for n_selected_assets in range(
                        self.parameter_ranges['n_selected_assets']['min'],
                        self.parameter_ranges['n_selected_assets']['max'] + 1,
                        self.parameter_ranges['n_selected_assets']['step']
                    ):
                        # Ensure n_selected_assets <= n_liquidity_assets
                        if n_selected_assets <= n_liquidity_assets:
                            combinations.append({
                                'window_size': window_size,
                                'n_liquidity_days': n_liquidity_days,
                                'n_liquidity_assets': n_liquidity_assets,
                                'n_selected_assets': n_selected_assets
                            })
        
        return combinations
    
    def _generate_coarse_grid(self) -> List[Dict]:
        """Generate coarse grid for initial exploration."""
        combinations = []
        
        # Use larger steps for coarse optimization
        window_sizes = [5, 15, 25, 35, 50]
        liquidity_days = [30, 60, 120, 180]
        liquidity_assets = [20, 60, 100, 140, 200]
        selected_assets = [5, 10, 15, 20, 25]
        
        for window_size in window_sizes:
            for n_liquidity_days in liquidity_days:
                for n_liquidity_assets in liquidity_assets:
                    for n_selected_assets in selected_assets:
                        if n_selected_assets <= n_liquidity_assets:
                            combinations.append({
                                'window_size': window_size,
                                'n_liquidity_days': n_liquidity_days,
                                'n_liquidity_assets': n_liquidity_assets,
                                'n_selected_assets': n_selected_assets
                            })
        
        return combinations
    
    def _generate_fine_grid(self) -> List[Dict]:
        """Generate fine grid around promising regions."""
        # This would typically be called after coarse optimization
        # For now, use smaller steps around current parameters
        combinations = []
        
        # Fine-tune around current parameters (10, 90, 50, 10)
        base_params = {
            'window_size': 10,
            'n_liquidity_days': 90,
            'n_liquidity_assets': 50,
            'n_selected_assets': 10
        }
        
        # Small variations around base parameters
        for window_size in range(max(5, base_params['window_size'] - 5), 
                                min(50, base_params['window_size'] + 6), 2):
            for n_liquidity_days in range(max(30, base_params['n_liquidity_days'] - 15),
                                        min(180, base_params['n_liquidity_days'] + 16), 5):
                for n_liquidity_assets in range(max(20, base_params['n_liquidity_assets'] - 20),
                                               min(200, base_params['n_liquidity_assets'] + 21), 10):
                    for n_selected_assets in range(max(5, base_params['n_selected_assets'] - 5),
                                                  min(25, base_params['n_selected_assets'] + 6), 2):
                        if n_selected_assets <= n_liquidity_assets:
                            combinations.append({
                                'window_size': window_size,
                                'n_liquidity_days': n_liquidity_days,
                                'n_liquidity_assets': n_liquidity_assets,
                                'n_selected_assets': n_selected_assets
                            })
        
        return combinations
    
    def _generate_focused_grid(self) -> List[Dict]:
        """Generate focused grid based on financial theory insights."""
        combinations = []
        
        # Focus on theoretically sound combinations
        # Short-term signals with recent liquidity
        short_term_combos = [
            {'window_size': 5, 'n_liquidity_days': 30},
            {'window_size': 10, 'n_liquidity_days': 45},
            {'window_size': 15, 'n_liquidity_days': 60}
        ]
        
        # Medium-term signals with balanced liquidity
        medium_term_combos = [
            {'window_size': 20, 'n_liquidity_days': 90},
            {'window_size': 25, 'n_liquidity_days': 120},
            {'window_size': 30, 'n_liquidity_days': 150}
        ]
        
        # Long-term signals with stable liquidity
        long_term_combos = [
            {'window_size': 35, 'n_liquidity_days': 120},
            {'window_size': 40, 'n_liquidity_days': 150},
            {'window_size': 50, 'n_liquidity_days': 180}
        ]
        
        all_combos = short_term_combos + medium_term_combos + long_term_combos
        
        # Combine with different asset selection parameters
        liquidity_assets_options = [30, 50, 100, 150]
        selected_assets_options = [5, 10, 15, 20]
        
        for combo in all_combos:
            for n_liquidity_assets in liquidity_assets_options:
                for n_selected_assets in selected_assets_options:
                    if n_selected_assets <= n_liquidity_assets:
                        combinations.append({
                            'window_size': combo['window_size'],
                            'n_liquidity_days': combo['n_liquidity_days'],
                            'n_liquidity_assets': n_liquidity_assets,
                            'n_selected_assets': n_selected_assets
                        })
        
        return combinations
    
    def get_recommended_ranges(self) -> Dict[str, Dict]:
        """
        Get recommended parameter ranges based on financial theory.
        
        Returns:
            Dictionary with recommended ranges for each parameter
        """
        return {
            'conservative': {
                'window_size': (15, 30),
                'n_liquidity_days': (60, 120),
                'n_liquidity_assets': (50, 100),
                'n_selected_assets': (8, 15),
                'rationale': 'Lower risk, stable parameters with good diversification'
            },
            'balanced': {
                'window_size': (10, 25),
                'n_liquidity_days': (45, 90),
                'n_liquidity_assets': (40, 80),
                'n_selected_assets': (10, 20),
                'rationale': 'Balanced risk-return with moderate responsiveness'
            },
            'aggressive': {
                'window_size': (5, 15),
                'n_liquidity_days': (30, 60),
                'n_liquidity_assets': (30, 60),
                'n_selected_assets': (5, 12),
                'rationale': 'Higher risk, more responsive to market changes'
            }
        }
    
    def validate_parameters(self, params: Dict) -> Tuple[bool, str]:
        """
        Validate parameter combination for logical consistency.
        
        Args:
            params: Parameter dictionary to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check if all required parameters are present
        required_params = ['window_size', 'n_liquidity_days', 'n_liquidity_assets', 'n_selected_assets']
        for param in required_params:
            if param not in params:
                return False, f"Missing required parameter: {param}"
        
        # Check parameter ranges
        for param, value in params.items():
            if param in self.parameter_ranges:
                param_range = self.parameter_ranges[param]
                if value < param_range['min'] or value > param_range['max']:
                    return False, f"{param} value {value} outside valid range [{param_range['min']}, {param_range['max']}]"
        
        # Check logical constraints
        if params['n_selected_assets'] > params['n_liquidity_assets']:
            return False, "n_selected_assets cannot be greater than n_liquidity_assets"
        
        # Check for reasonable combinations
        if params['window_size'] > params['n_liquidity_days']:
            return False, "window_size should not exceed n_liquidity_days for signal stability"
        
        return True, "Parameters are valid"
