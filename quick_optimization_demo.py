"""
Quick Optimization Demo

A simplified version to quickly test the optimization framework
with your current parameters and a few variations.
"""

import pandas as pd
import numpy as np
from parameter_optimizer import ParameterOptimizer
from optimization_config import OptimizationConfig
import warnings
warnings.filterwarnings('ignore')


def quick_demo():
    """Run a quick optimization demo."""
    
    print("="*60)
    print("QUICK PARAMETER OPTIMIZATION DEMO")
    print("="*60)
    
    # Initialize optimizer
    optimizer = ParameterOptimizer(
        db_name='cotacoes copy.db',
        total_value=25000,
        value_per_asset=5000,
        offer_spread=0.1,
        risk_free_rate=0.1
    )
    
    # Your current parameters
    current_params = {
        'window_size': 10,
        'n_liquidity_days': 90,
        'n_liquidity_assets': 50,
        'n_selected_assets': 10
    }
    
    print("Testing Current Parameters:")
    for param, value in current_params.items():
        print(f"  {param}: {value}")
    print()
    
    # Test current parameters
    print("Running backtest with current parameters...")
    trades, metrics = optimizer.run_backtest(current_params)
    
    print("\nCurrent Parameters Performance:")
    print(f"  Total Trades: {metrics['total_trades']}")
    print(f"  Total Return: {metrics['total_return']:.2%}")
    print(f"  Annual Return: {metrics['annual_return']:.2%}")
    print(f"  Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
    print(f"  Max Drawdown: {metrics['max_drawdown']:.2%}")
    print(f"  Win Rate: {metrics['win_rate']:.1f}%")
    print(f"  Risk-Adjusted Score: {metrics['risk_adjusted_score']:.4f}")
    print()
    
    # Test a few parameter variations
    print("Testing Parameter Variations:")
    print("-" * 40)
    
    variations = [
        {'window_size': 5, 'n_liquidity_days': 60, 'n_liquidity_assets': 40, 'n_selected_assets': 8},
        {'window_size': 15, 'n_liquidity_days': 120, 'n_liquidity_assets': 60, 'n_selected_assets': 12},
        {'window_size': 20, 'n_liquidity_days': 90, 'n_liquidity_assets': 80, 'n_selected_assets': 15},
        {'window_size': 8, 'n_liquidity_days': 75, 'n_liquidity_assets': 45, 'n_selected_assets': 10},
    ]
    
    results = []
    
    for i, params in enumerate(variations, 1):
        print(f"Testing Variation {i}: WS={params['window_size']}, LD={params['n_liquidity_days']}, "
              f"LA={params['n_liquidity_assets']}, SA={params['n_selected_assets']}")
        
        trades_var, metrics_var = optimizer.run_backtest(params)
        
        result = {
            'variation': i,
            **params,
            **metrics_var
        }
        results.append(result)
        
        print(f"  Score: {metrics_var['risk_adjusted_score']:.4f}, "
              f"Sharpe: {metrics_var['sharpe_ratio']:.3f}, "
              f"Trades: {metrics_var['total_trades']}")
    
    # Compare results
    print("\n" + "="*60)
    print("COMPARISON RESULTS")
    print("="*60)
    
    # Add current parameters to results for comparison
    current_result = {
        'variation': 'Current',
        **current_params,
        **metrics
    }
    
    all_results = [current_result] + results
    results_df = pd.DataFrame(all_results)
    
    # Sort by risk-adjusted score
    results_df = results_df.sort_values('risk_adjusted_score', ascending=False)
    
    print("\nRanked by Risk-Adjusted Score:")
    print("-" * 80)
    print(f"{'Rank':<4} {'Variation':<8} {'WS':<3} {'LD':<4} {'LA':<4} {'SA':<3} {'Score':<8} {'Sharpe':<8} {'Trades':<6}")
    print("-" * 80)
    
    for i, (_, row) in enumerate(results_df.iterrows(), 1):
        print(f"{i:<4} {str(row['variation']):<8} {int(row['window_size']):<3} "
              f"{int(row['n_liquidity_days']):<4} {int(row['n_liquidity_assets']):<4} "
              f"{int(row['n_selected_assets']):<3} {row['risk_adjusted_score']:<8.4f} "
              f"{row['sharpe_ratio']:<8.3f} {int(row['total_trades']):<6}")
    
    # Find best performing variation
    best_row = results_df.iloc[0]
    
    print(f"\n🏆 BEST PERFORMING CONFIGURATION:")
    print(f"   Variation: {best_row['variation']}")
    print(f"   Parameters: WS={int(best_row['window_size'])}, LD={int(best_row['n_liquidity_days'])}, "
          f"LA={int(best_row['n_liquidity_assets'])}, SA={int(best_row['n_selected_assets'])}")
    print(f"   Risk-Adjusted Score: {best_row['risk_adjusted_score']:.4f}")
    
    if best_row['variation'] != 'Current':
        improvement = ((best_row['risk_adjusted_score'] / metrics['risk_adjusted_score']) - 1) * 100
        print(f"   Improvement over current: {improvement:.1f}%")
        
        if improvement > 5:
            print("   ✅ Significant improvement detected!")
            print("   💡 Recommendation: Consider adopting these parameters")
        elif improvement > 0:
            print("   ⚠️  Minor improvement detected")
            print("   💡 Recommendation: Further testing recommended")
        else:
            print("   ❌ No improvement over current parameters")
    else:
        print("   ✅ Current parameters are already optimal among tested variations")
    
    # Risk assessment
    print(f"\n📊 RISK ASSESSMENT:")
    print(f"   Max Drawdown: {best_row['max_drawdown']:.2%}")
    if best_row['max_drawdown'] > 0.25:
        print("   ⚠️  WARNING: High drawdown risk!")
    elif best_row['max_drawdown'] > 0.15:
        print("   ⚠️  CAUTION: Moderate drawdown risk")
    else:
        print("   ✅ Acceptable drawdown risk")
    
    print(f"   Sharpe Ratio: {best_row['sharpe_ratio']:.3f}")
    if best_row['sharpe_ratio'] < 0.5:
        print("   ⚠️  WARNING: Low risk-adjusted returns!")
    elif best_row['sharpe_ratio'] < 1.0:
        print("   ⚠️  CAUTION: Below-average risk-adjusted returns")
    else:
        print("   ✅ Good risk-adjusted returns")
    
    print(f"   Win Rate: {best_row['win_rate']:.1f}%")
    if best_row['win_rate'] < 45:
        print("   ⚠️  WARNING: Low win rate!")
    elif best_row['win_rate'] < 55:
        print("   ⚠️  CAUTION: Below-average win rate")
    else:
        print("   ✅ Good win rate")
    
    print("\n" + "="*60)
    print("DEMO COMPLETE!")
    print("="*60)
    print("\n💡 Next Steps:")
    print("   1. Run full optimization with 'python run_optimization.py'")
    print("   2. Perform walk-forward analysis for validation")
    print("   3. Implement risk management measures")
    print("   4. Monitor performance in live trading")


if __name__ == "__main__":
    quick_demo()
