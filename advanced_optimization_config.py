"""
Advanced Optimization Configuration Module

This module defines sophisticated parameter ranges and optimization strategies based on 
financial theory, market microstructure, and behavioral finance principles.
"""

from typing import Dict, List, Tuple, Optional
import numpy as np
import itertools


class AdvancedOptimizationConfig:
    """
    Advanced configuration class for trading system parameter optimization.
    
    Uses financial theory to create intelligent parameter exploration strategies:
    
    1. Market Regime Theory: Different parameters for different market conditions
    2. Signal Decay Theory: Optimal signal memory based on market efficiency
    3. Liquidity Theory: Optimal liquidity assessment periods
    4. Portfolio Theory: Optimal diversification levels
    5. Behavioral Finance: Parameter stability and robustness
    """
    
    def __init__(self):
        """Initialize advanced optimization configuration."""
        
        # Extended parameter ranges as specified
        self.parameter_ranges = {
            'window_size': {
                'min': 1, 'max': 252, 'step': 1,
                'description': 'Moving sum window for signal calculation',
                'financial_rationale': 'Signal memory from daily to annual patterns',
                'theory_zones': {
                    'ultra_short': (1, 5),      # High frequency, noise sensitive
                    'short_term': (5, 21),      # Weekly to monthly patterns  
                    'medium_term': (21, 63),    # Monthly to quarterly patterns
                    'long_term': (63, 126),     # Quarterly to semi-annual
                    'ultra_long': (126, 252)    # Semi-annual to annual
                }
            },
            'n_liquidity_days': {
                'min': 5, 'max': 252, 'step': 1,
                'description': 'Days for liquidity calculation',
                'financial_rationale': 'Liquidity assessment from weekly to annual',
                'theory_zones': {
                    'immediate': (5, 21),       # Recent liquidity patterns
                    'short_term': (21, 63),     # Monthly to quarterly
                    'medium_term': (63, 126),   # Quarterly to semi-annual  
                    'long_term': (126, 252)     # Semi-annual to annual
                }
            },
            'n_liquidity_assets': {
                'min': 20, 'max': 300, 'step': 1,
                'description': 'Number of liquid assets to consider',
                'financial_rationale': 'Universe size vs liquidity quality trade-off',
                'theory_zones': {
                    'elite': (20, 50),          # Most liquid assets only
                    'quality': (50, 100),       # High quality liquid assets
                    'balanced': (100, 200),     # Balanced liquidity/diversity
                    'broad': (200, 300)         # Maximum universe coverage
                }
            },
            'n_selected_assets': {
                'min': 1, 'max': 20, 'step': 1,
                'description': 'Number of assets to trade daily',
                'financial_rationale': 'Concentration vs diversification optimization',
                'theory_zones': {
                    'concentrated': (1, 5),     # High alpha, high risk
                    'focused': (5, 10),         # Moderate concentration
                    'balanced': (10, 15),       # Good diversification
                    'diversified': (15, 20)     # Maximum diversification
                }
            }
        }
        
        # Financial theory-based optimization strategies
        self.optimization_strategies = {
            'market_microstructure': {
                'description': 'Based on market microstructure theory',
                'rationale': 'Different timeframes capture different market inefficiencies',
                'combinations': [
                    # Ultra-short term momentum
                    {'window_size': (1, 5), 'n_liquidity_days': (5, 21)},
                    # Short-term reversal
                    {'window_size': (5, 21), 'n_liquidity_days': (21, 63)},
                    # Medium-term trend
                    {'window_size': (21, 63), 'n_liquidity_days': (63, 126)},
                    # Long-term momentum
                    {'window_size': (63, 126), 'n_liquidity_days': (126, 252)}
                ]
            },
            'behavioral_finance': {
                'description': 'Based on behavioral finance patterns',
                'rationale': 'Exploits behavioral biases at different time horizons',
                'combinations': [
                    # Overreaction (short-term reversal)
                    {'window_size': (1, 10), 'n_liquidity_days': (5, 30)},
                    # Momentum (medium-term continuation)
                    {'window_size': (10, 50), 'n_liquidity_days': (30, 90)},
                    # Long-term reversal
                    {'window_size': (50, 150), 'n_liquidity_days': (90, 180)}
                ]
            },
            'portfolio_theory': {
                'description': 'Based on modern portfolio theory',
                'rationale': 'Optimal diversification for different risk levels',
                'combinations': [
                    # High risk, high return
                    {'n_liquidity_assets': (20, 60), 'n_selected_assets': (1, 5)},
                    # Moderate risk
                    {'n_liquidity_assets': (60, 150), 'n_selected_assets': (5, 12)},
                    # Conservative
                    {'n_liquidity_assets': (150, 300), 'n_selected_assets': (12, 20)}
                ]
            },
            'adaptive_markets': {
                'description': 'Based on adaptive market hypothesis',
                'rationale': 'Parameters adapt to changing market conditions',
                'combinations': [
                    # High adaptability
                    {'window_size': (1, 21), 'n_liquidity_days': (5, 63)},
                    # Medium adaptability  
                    {'window_size': (21, 63), 'n_liquidity_days': (63, 126)},
                    # Low adaptability
                    {'window_size': (63, 252), 'n_liquidity_days': (126, 252)}
                ]
            }
        }
        
        # Intelligent sampling strategies
        self.sampling_strategies = {
            'fibonacci_sampling': {
                'description': 'Use Fibonacci numbers for natural market rhythms',
                'window_sizes': [1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233],
                'liquidity_days': [5, 8, 13, 21, 34, 55, 89, 144, 233]
            },
            'trading_calendar': {
                'description': 'Based on trading calendar patterns',
                'window_sizes': [1, 5, 10, 21, 42, 63, 126, 189, 252],  # Daily, weekly, bi-weekly, monthly, etc.
                'liquidity_days': [5, 21, 42, 63, 126, 189, 252]
            },
            'statistical_sampling': {
                'description': 'Statistically optimal sampling points',
                'method': 'latin_hypercube'  # For efficient parameter space exploration
            }
        }
    
    def get_theoretical_combinations(self, strategy: str = 'comprehensive') -> List[Dict]:
        """
        Generate parameter combinations based on financial theory.
        
        Args:
            strategy: Optimization strategy ('comprehensive', 'market_microstructure', 
                     'behavioral_finance', 'portfolio_theory', 'adaptive_markets')
        
        Returns:
            List of parameter dictionaries
        """
        if strategy == 'comprehensive':
            return self._generate_comprehensive_combinations()
        elif strategy in self.optimization_strategies:
            return self._generate_strategy_combinations(strategy)
        else:
            raise ValueError(f"Unknown strategy: {strategy}")
    
    def _generate_comprehensive_combinations(self) -> List[Dict]:
        """Generate comprehensive parameter combinations using all strategies."""
        all_combinations = []
        
        # 1. Fibonacci-based combinations (natural market rhythms)
        fib_combinations = self._generate_fibonacci_combinations()
        all_combinations.extend(fib_combinations)
        
        # 2. Trading calendar-based combinations
        calendar_combinations = self._generate_calendar_combinations()
        all_combinations.extend(calendar_combinations)
        
        # 3. Theory-based strategic combinations
        for strategy in self.optimization_strategies.keys():
            strategy_combinations = self._generate_strategy_combinations(strategy)
            all_combinations.extend(strategy_combinations)
        
        # 4. Current parameter neighborhood (fine-tuning)
        current_neighborhood = self._generate_current_neighborhood()
        all_combinations.extend(current_neighborhood)
        
        # Remove duplicates
        unique_combinations = []
        seen = set()
        for combo in all_combinations:
            combo_tuple = tuple(sorted(combo.items()))
            if combo_tuple not in seen:
                seen.add(combo_tuple)
                unique_combinations.append(combo)
        
        return unique_combinations
    
    def _generate_fibonacci_combinations(self) -> List[Dict]:
        """Generate combinations using Fibonacci numbers."""
        combinations = []
        fib_sampling = self.sampling_strategies['fibonacci_sampling']
        
        # Asset selection combinations
        asset_combinations = [
            {'n_liquidity_assets': 21, 'n_selected_assets': 1},
            {'n_liquidity_assets': 34, 'n_selected_assets': 2},
            {'n_liquidity_assets': 55, 'n_selected_assets': 3},
            {'n_liquidity_assets': 89, 'n_selected_assets': 5},
            {'n_liquidity_assets': 144, 'n_selected_assets': 8},
            {'n_liquidity_assets': 233, 'n_selected_assets': 13},
            {'n_liquidity_assets': 300, 'n_selected_assets': 20}
        ]
        
        for window_size in fib_sampling['window_sizes']:
            for liquidity_days in fib_sampling['liquidity_days']:
                for asset_combo in asset_combinations:
                    if (asset_combo['n_selected_assets'] <= asset_combo['n_liquidity_assets'] and
                        window_size <= liquidity_days):  # Logical constraint
                        combinations.append({
                            'window_size': window_size,
                            'n_liquidity_days': liquidity_days,
                            **asset_combo
                        })
        
        return combinations
    
    def _generate_calendar_combinations(self) -> List[Dict]:
        """Generate combinations based on trading calendar."""
        combinations = []
        calendar_sampling = self.sampling_strategies['trading_calendar']
        
        # Asset combinations for different risk profiles
        risk_profiles = [
            {'profile': 'aggressive', 'n_liquidity_assets': 50, 'n_selected_assets': 3},
            {'profile': 'moderate', 'n_liquidity_assets': 100, 'n_selected_assets': 8},
            {'profile': 'conservative', 'n_liquidity_assets': 200, 'n_selected_assets': 15}
        ]
        
        for window_size in calendar_sampling['window_sizes']:
            for liquidity_days in calendar_sampling['liquidity_days']:
                for profile in risk_profiles:
                    if window_size <= liquidity_days:
                        combinations.append({
                            'window_size': window_size,
                            'n_liquidity_days': liquidity_days,
                            'n_liquidity_assets': profile['n_liquidity_assets'],
                            'n_selected_assets': profile['n_selected_assets']
                        })
        
        return combinations
    
    def _generate_strategy_combinations(self, strategy: str) -> List[Dict]:
        """Generate combinations for a specific strategy."""
        combinations = []
        strategy_config = self.optimization_strategies[strategy]
        
        for combo_config in strategy_config['combinations']:
            # Generate parameter ranges for this combination
            param_ranges = {}
            for param, (min_val, max_val) in combo_config.items():
                if param == 'window_size':
                    param_ranges[param] = self._get_smart_range(min_val, max_val, 'window_size')
                elif param == 'n_liquidity_days':
                    param_ranges[param] = self._get_smart_range(min_val, max_val, 'liquidity_days')
                elif param == 'n_liquidity_assets':
                    param_ranges[param] = self._get_smart_range(min_val, max_val, 'assets')
                elif param == 'n_selected_assets':
                    param_ranges[param] = self._get_smart_range(min_val, max_val, 'selected')
            
            # Generate all combinations for this strategy
            if len(param_ranges) == 2:
                # Two parameters specified
                param_names = list(param_ranges.keys())
                for val1 in param_ranges[param_names[0]]:
                    for val2 in param_ranges[param_names[1]]:
                        # Add default values for other parameters
                        combo = {param_names[0]: val1, param_names[1]: val2}
                        combo = self._add_default_parameters(combo)
                        if self._validate_combination(combo):
                            combinations.append(combo)
        
        return combinations
    
    def _get_smart_range(self, min_val: int, max_val: int, param_type: str) -> List[int]:
        """Generate smart sampling points within a range."""
        if param_type == 'window_size':
            # Use key technical analysis periods
            key_points = [1, 2, 3, 5, 8, 10, 13, 15, 20, 21, 30, 42, 50, 63, 100, 126, 200, 252]
            return [p for p in key_points if min_val <= p <= max_val]
        elif param_type == 'liquidity_days':
            # Use key liquidity assessment periods
            key_points = [5, 10, 15, 21, 30, 42, 60, 90, 126, 180, 252]
            return [p for p in key_points if min_val <= p <= max_val]
        elif param_type == 'assets':
            # Use round numbers for asset counts
            step = max(5, (max_val - min_val) // 10)
            return list(range(min_val, max_val + 1, step))
        elif param_type == 'selected':
            # Use all values for selected assets (small range)
            return list(range(min_val, min(max_val + 1, 21)))
        else:
            # Default linear sampling
            step = max(1, (max_val - min_val) // 20)
            return list(range(min_val, max_val + 1, step))
    
    def _add_default_parameters(self, combo: Dict) -> Dict:
        """Add default values for missing parameters."""
        defaults = {
            'window_size': 10,
            'n_liquidity_days': 90,
            'n_liquidity_assets': 100,
            'n_selected_assets': 10
        }
        
        for param, default_val in defaults.items():
            if param not in combo:
                combo[param] = default_val
        
        return combo
    
    def _generate_current_neighborhood(self) -> List[Dict]:
        """Generate fine-tuning combinations around current parameters."""
        current = {'window_size': 10, 'n_liquidity_days': 90, 'n_liquidity_assets': 50, 'n_selected_assets': 10}
        combinations = []
        
        # Small variations around current parameters
        variations = {
            'window_size': [-3, -2, -1, 0, 1, 2, 3, 5, 8],
            'n_liquidity_days': [-15, -10, -5, 0, 5, 10, 15, 30, 45],
            'n_liquidity_assets': [-10, -5, 0, 5, 10, 20, 30, 50],
            'n_selected_assets': [-3, -2, -1, 0, 1, 2, 3, 5]
        }
        
        for ws_var in variations['window_size']:
            for ld_var in variations['n_liquidity_days']:
                for la_var in variations['n_liquidity_assets']:
                    for sa_var in variations['n_selected_assets']:
                        combo = {
                            'window_size': max(1, current['window_size'] + ws_var),
                            'n_liquidity_days': max(5, current['n_liquidity_days'] + ld_var),
                            'n_liquidity_assets': max(20, min(300, current['n_liquidity_assets'] + la_var)),
                            'n_selected_assets': max(1, min(20, current['n_selected_assets'] + sa_var))
                        }
                        
                        if self._validate_combination(combo):
                            combinations.append(combo)
        
        return combinations
    
    def _validate_combination(self, combo: Dict) -> bool:
        """Validate parameter combination for logical consistency."""
        # Basic range checks
        if not (1 <= combo['window_size'] <= 252):
            return False
        if not (5 <= combo['n_liquidity_days'] <= 252):
            return False
        if not (20 <= combo['n_liquidity_assets'] <= 300):
            return False
        if not (1 <= combo['n_selected_assets'] <= 20):
            return False
        
        # Logical constraints
        if combo['n_selected_assets'] > combo['n_liquidity_assets']:
            return False
        if combo['window_size'] > combo['n_liquidity_days']:
            return False
        
        return True
    
    def get_optimization_phases(self) -> List[Dict]:
        """
        Get multi-phase optimization strategy.
        
        Returns:
            List of optimization phases with different strategies
        """
        return [
            {
                'phase': 1,
                'name': 'Theoretical Exploration',
                'strategy': 'comprehensive',
                'description': 'Explore parameter space using financial theory',
                'max_combinations': 2000,
                'parallel_jobs': 8
            },
            {
                'phase': 2,
                'name': 'Top Performers Analysis',
                'strategy': 'top_refinement',
                'description': 'Refine around top 10% performers from phase 1',
                'max_combinations': 1000,
                'parallel_jobs': 6
            },
            {
                'phase': 3,
                'name': 'Final Optimization',
                'strategy': 'final_tuning',
                'description': 'Fine-tune best parameters with small variations',
                'max_combinations': 500,
                'parallel_jobs': 4
            }
        ]
    
    def get_performance_targets(self) -> Dict:
        """Get performance targets for different optimization goals."""
        return {
            'conservative': {
                'min_sharpe_ratio': 1.5,
                'max_drawdown': 0.10,
                'min_win_rate': 60.0,
                'min_trades': 100,
                'target_annual_return': 0.15
            },
            'balanced': {
                'min_sharpe_ratio': 2.0,
                'max_drawdown': 0.15,
                'min_win_rate': 55.0,
                'min_trades': 200,
                'target_annual_return': 0.25
            },
            'aggressive': {
                'min_sharpe_ratio': 2.5,
                'max_drawdown': 0.20,
                'min_win_rate': 50.0,
                'min_trades': 300,
                'target_annual_return': 0.35
            }
        }
