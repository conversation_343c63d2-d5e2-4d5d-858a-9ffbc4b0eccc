## Banco de cotações:
- Nome do arquivo: cotacoes.db
- SQLite DDL:
  ```sql
  CREATE TABLE cotacoes (
          symbol TEXT,
          date DATE,
          open REAL,
          high REAL,
          low REAL,
          close REAL,
          volume INTEGER,
          PRIMARY KEY (symbol, date)
      );
  
  CREATE INDEX idx_symbol ON cotacoes (symbol);
  CREATE INDEX idx_date ON cotacoes (date);
  ```

# Seleção dos ativos
- A seleção dos ativos será com base de dois filtros: volume financeiro e o indicador intraBin.
- O primeiro filtro é o volume financeiro. Devem ser considerados apenas os N_LIQUIDY_ASSETS ativos com maior volume financeiro (volume*close) no último mês para o próximo filtro (indicador intraBin).
- O indicador intraBin é calculado de acordo com a lógica abaixo:
  ```python
  intraBin = 0
  for i in range(1, WINDOW_SIZE):
      if close[i] > open[i]:
          intraBin += 1
      elif close[i] < open[i]:
          intraBin -= 1
      else:
          intraBin += 0
  ```
- Serão selecionados para operar os N_SELECTED_ASSETS ativos com os menores valores desse indicador.
- Garanta que o indicador utilize apenas informações do dia anterior ao dia das operações (ou seja, o dia anterior ao entry_date).

# Lógica das operações:
- Serão operados N_SELECTED_ASSETS ativos;
- As operações terão a duração de uma noite (overnight trade);
- Os ativos serão comprados sempre no leilão de fechamento;
- E as operações serão finalizadas sempre no leilão de abertura;
- Cada operação terão um valor fixo de R$ VALUE_PER_ASSET;
- A quantidade de ativos comprados será definida por multiplos de 100 considerando o fechamento anterior;
- Se a mínima quantidade de ativos comprados for menor que 100, será comprado o próximo ativos da lista de seleção;
- O valor total operado será definido por VALUE_TOTAL, aonde VALUE_TOTAL <= VALUE_PER_ASSET * N_OPERATED_ASSETS.
- Se o ativo não possuir preço de fechamento, deverá ser desconsiderado para operar (pula para o próximo da lista).
- Se o ativo não possuir preço de abertura no próximo dia, a operação não será finalizada. Deverá ser mantida em aberto e o valor dessa operação deve ser descontado do VALUE_TOTAL para as próximas operações. A operação só será finalizada no dia que o preço de abertura estiver disponível.
- Segue abaixo o algoritmo que inclui a lógica de abertura das operações:
  ```
  open_positions_value = get_open_positions_value()
  available_capital = open_positions_value ? VALUE_TOTAL - open_positions_value : VALUE_TOTAL
  selected_assets = select_assets_for_date()
  for asset in selected_assets:
      if available_capital <= VALUE_PER_ASSET:
          break
      asset_quantity = int(VALUE_PER_ASSET / (asset['close'] * 100)) * 100
      if asset_quantity = 0:
          continue
      else:
          available_capital -= asset_quantity * asset['close']
          open_position(asset, asset_quantity) 
  ```

## Banco de operações:
- Nome do arquivo: operacoes_bt.db
- SQLite DDL:
  ```sql
  -- Tabela de operações (registro de trades)
  CREATE TABLE operations (
      -- Identificador único e autoincrementável da operação
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      
      -- Símbolo do ativo financeiro operado (ex: PETR4, VALE3)
      symbol TEXT NOT NULL,
      
      -- Data em que a operação foi iniciada (leilão de fechamento)
      entry_date DATE NOT NULL,
      
      -- Preço de entrada (preço de fechamento no dia entry_date)
      entry_price REAL NOT NULL,
      
      -- Quantidade de ativos comprados (sempre em múltiplos de 100)
      quantity INTEGER NOT NULL,
      
      -- Status da operação: 
      -- 'OPEN' = operação em aberto (aguardando fechamento)
      -- 'CLOSED' = operação finalizada
      status TEXT NOT NULL CHECK (status IN ('OPEN', 'CLOSED')),
      
      -- Data em que a operação DEVERIA ser encerrada (próximo dia útil após entry_date)
      intended_exit_date DATE NOT NULL,
      
      -- Data REAL em que a operação foi encerrada (pode diferir de intended_exit_date)
      -- NULL enquanto a operação estiver aberta
      actual_exit_date DATE,
      
      -- Preço de saída (preço de abertura no dia actual_exit_date)
      -- NULL enquanto a operação estiver aberta
      exit_price REAL,
      
      -- Valor total investido na operação (quantity * entry_price)
      initial_value REAL NOT NULL,
      
      -- Resultado financeiro da operação (profit/loss) após fechamento:
      -- (exit_price * quantity) - initial_value
      -- NULL enquanto a operação estiver aberta
      realized_pnl REAL,
      
      -- Timestamp automático de quando o registro foi criado
      -- Útil para auditoria e ordenação
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );
  ```
  
# Prompt Inicial:
- Com base nas regras e estrutura acima, crie um backtest completo considerando a partir de 2021-01-01 até a data de 2025-04-25
- Considere os seguinte parâmetros:
  - N_SELECTED_ASSETS = 25
  - N_LIQUIDY_ASSETS = 300
  - WINDOW_SIZE = 200
  - VALUE_PER_ASSET = 5000
  - VALUE_TOTAL = 50000
- Use pandas para uma análise mais rápida e eficiente dos dados.
- Quando finalizado o backtest, entregue um relatório completo para análise da estratégia de trades.



O moving sum with WINDOW_SIZE that sum the binary values from this logic:
if d-1 open > d-2 close: 1
if d-1 open < d-2 close: -1
if d-1 open = d-2 close: 0

The trade system should use the N_SELECTED_ASSETS with the highest values of this moving sum.