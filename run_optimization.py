"""
Run Parameter Optimization Script

This script executes the parameter optimization for the trading system
with your current parameters as a baseline and explores optimal configurations.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from parameter_optimizer import ParameterOptimizer
from optimization_config import OptimizationConfig
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


def main():
    """Main optimization execution function."""
    
    print("="*80)
    print("TRADING SYSTEM PARAMETER OPTIMIZATION")
    print("="*80)
    print()
    
    # Initialize optimizer with your current settings
    optimizer = ParameterOptimizer(
        db_name='cotacoes copy.db',
        total_value=25000,
        value_per_asset=5000,
        offer_spread=0.1,
        risk_free_rate=0.1
    )
    
    # Current parameters to benchmark against
    current_params = {
        'window_size': 10,
        'n_liquidity_days': 90,
        'n_liquidity_assets': 50,
        'n_selected_assets': 10
    }
    
    print("Current Parameters:")
    for param, value in current_params.items():
        print(f"  {param}: {value}")
    print()
    
    # Define optimization period (adjust dates based on your data availability)
    start_date = '2022-01-01'  # Adjust based on your data
    end_date = '2024-12-31'    # Adjust based on your data
    
    print(f"Optimization Period: {start_date} to {end_date}")
    print()
    
    # Step 1: Benchmark current parameters
    print("Step 1: Benchmarking Current Parameters")
    print("-" * 50)
    
    current_trades, current_metrics = optimizer.run_backtest(
        current_params, start_date, end_date
    )
    
    print("Current Parameters Performance:")
    print(f"  Total Return: {current_metrics['total_return']:.2%}")
    print(f"  Annual Return: {current_metrics['annual_return']:.2%}")
    print(f"  Sharpe Ratio: {current_metrics['sharpe_ratio']:.3f}")
    print(f"  Calmar Ratio: {current_metrics['calmar_ratio']:.3f}")
    print(f"  Max Drawdown: {current_metrics['max_drawdown']:.2%}")
    print(f"  Win Rate: {current_metrics['win_rate']:.1f}%")
    print(f"  Total Trades: {current_metrics['total_trades']}")
    print(f"  Risk-Adjusted Score: {current_metrics['risk_adjusted_score']:.4f}")
    print()
    
    # Step 2: Coarse optimization
    print("Step 2: Coarse Parameter Optimization")
    print("-" * 50)
    
    coarse_results = optimizer.optimize_parameters(
        optimization_type='coarse',
        start_date=start_date,
        end_date=end_date,
        n_jobs=4,  # Adjust based on your CPU cores
        save_results=True
    )
    
    if not coarse_results.empty:
        best_coarse = coarse_results.iloc[0]
        print("\nBest Parameters from Coarse Search:")
        print(f"  window_size: {int(best_coarse['window_size'])}")
        print(f"  n_liquidity_days: {int(best_coarse['n_liquidity_days'])}")
        print(f"  n_liquidity_assets: {int(best_coarse['n_liquidity_assets'])}")
        print(f"  n_selected_assets: {int(best_coarse['n_selected_assets'])}")
        print(f"  Risk-Adjusted Score: {best_coarse['risk_adjusted_score']:.4f}")
        print(f"  Improvement vs Current: {((best_coarse['risk_adjusted_score'] / current_metrics['risk_adjusted_score']) - 1) * 100:.1f}%")
    
    # Step 3: Fine optimization around best coarse results
    print("\nStep 3: Fine Parameter Optimization")
    print("-" * 50)
    
    fine_results = optimizer.optimize_parameters(
        optimization_type='fine',
        start_date=start_date,
        end_date=end_date,
        n_jobs=4,
        save_results=True
    )
    
    if not fine_results.empty:
        best_fine = fine_results.iloc[0]
        print("\nBest Parameters from Fine Search:")
        print(f"  window_size: {int(best_fine['window_size'])}")
        print(f"  n_liquidity_days: {int(best_fine['n_liquidity_days'])}")
        print(f"  n_liquidity_assets: {int(best_fine['n_liquidity_assets'])}")
        print(f"  n_selected_assets: {int(best_fine['n_selected_assets'])}")
        print(f"  Risk-Adjusted Score: {best_fine['risk_adjusted_score']:.4f}")
        print(f"  Improvement vs Current: {((best_fine['risk_adjusted_score'] / current_metrics['risk_adjusted_score']) - 1) * 100:.1f}%")
    
    # Step 4: Walk-forward analysis on best parameters
    print("\nStep 4: Walk-Forward Analysis")
    print("-" * 50)
    
    best_params = {
        'window_size': int(best_fine['window_size']),
        'n_liquidity_days': int(best_fine['n_liquidity_days']),
        'n_liquidity_assets': int(best_fine['n_liquidity_assets']),
        'n_selected_assets': int(best_fine['n_selected_assets'])
    }
    
    wf_results = optimizer.walk_forward_analysis(
        best_params,
        start_date=start_date,
        end_date=end_date,
        n_periods=4,
        train_ratio=0.75
    )
    
    print("Walk-Forward Analysis Results:")
    print(f"  Average Return: {wf_results['avg_return']:.2%}")
    print(f"  Return Std Dev: {wf_results['std_return']:.2%}")
    print(f"  Average Sharpe: {wf_results['avg_sharpe']:.3f}")
    print(f"  Consistency Score: {wf_results['consistency_score']:.1%}")
    print(f"  Average Trades per Period: {wf_results['avg_trades_per_period']:.0f}")
    
    # Step 5: Parameter sensitivity analysis
    print("\nStep 5: Parameter Sensitivity Analysis")
    print("-" * 50)
    
    sensitivity = optimizer.analyze_parameter_sensitivity()
    
    if sensitivity:
        print("Parameter Correlations with Risk-Adjusted Score:")
        for param, corr in sensitivity['correlations'].items():
            print(f"  {param}: {corr['risk_adjusted_score']:.3f}")
        
        print("\nOptimal Parameter Ranges (Top 20% performers):")
        for param, stats in sensitivity['optimal_ranges'].items():
            print(f"  {param}: {stats['min']:.0f} - {stats['max']:.0f} (mean: {stats['mean']:.1f})")
    
    # Step 6: Generate optimization report
    print("\nStep 6: Generating Optimization Report")
    print("-" * 50)
    
    generate_optimization_report(
        optimizer, current_params, current_metrics, 
        coarse_results, fine_results, wf_results, sensitivity
    )
    
    # Step 7: Create visualizations
    print("\nStep 7: Creating Visualizations")
    print("-" * 50)
    
    create_optimization_plots(optimizer, coarse_results, fine_results)
    
    print("\n" + "="*80)
    print("OPTIMIZATION COMPLETE!")
    print("="*80)
    
    # Final recommendations
    print("\nFINAL RECOMMENDATIONS:")
    print("-" * 30)
    
    if not fine_results.empty:
        improvement = ((best_fine['risk_adjusted_score'] / current_metrics['risk_adjusted_score']) - 1) * 100
        
        if improvement > 10:
            print("🎯 STRONG RECOMMENDATION: Adopt the optimized parameters")
            print(f"   Expected improvement: {improvement:.1f}%")
        elif improvement > 5:
            print("✅ MODERATE RECOMMENDATION: Consider adopting optimized parameters")
            print(f"   Expected improvement: {improvement:.1f}%")
        elif improvement > 0:
            print("⚠️  WEAK RECOMMENDATION: Minor improvement possible")
            print(f"   Expected improvement: {improvement:.1f}%")
        else:
            print("❌ NO RECOMMENDATION: Current parameters are already optimal")
        
        print(f"\nOptimal Parameters:")
        print(f"  window_size: {int(best_fine['window_size'])}")
        print(f"  n_liquidity_days: {int(best_fine['n_liquidity_days'])}")
        print(f"  n_liquidity_assets: {int(best_fine['n_liquidity_assets'])}")
        print(f"  n_selected_assets: {int(best_fine['n_selected_assets'])}")
        
        # Risk assessment
        if best_fine['max_drawdown'] > 0.25:
            print("\n⚠️  WARNING: High maximum drawdown detected!")
            print("   Consider implementing additional risk management measures.")
        
        if best_fine['sharpe_ratio'] < 1.0:
            print("\n⚠️  WARNING: Low Sharpe ratio!")
            print("   Strategy may not provide adequate risk-adjusted returns.")


def generate_optimization_report(optimizer, current_params, current_metrics, 
                               coarse_results, fine_results, wf_results, sensitivity):
    """Generate comprehensive optimization report."""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f'optimization_report_{timestamp}.txt'
    
    with open(report_file, 'w') as f:
        f.write("TRADING SYSTEM PARAMETER OPTIMIZATION REPORT\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Current parameters section
        f.write("CURRENT PARAMETERS BASELINE\n")
        f.write("-" * 30 + "\n")
        for param, value in current_params.items():
            f.write(f"{param}: {value}\n")
        f.write(f"\nPerformance Metrics:\n")
        for metric, value in current_metrics.items():
            if isinstance(value, float):
                if 'rate' in metric or 'return' in metric:
                    f.write(f"  {metric}: {value:.2%}\n")
                else:
                    f.write(f"  {metric}: {value:.4f}\n")
            else:
                f.write(f"  {metric}: {value}\n")
        f.write("\n")
        
        # Optimization results
        if not fine_results.empty:
            best = fine_results.iloc[0]
            f.write("OPTIMIZED PARAMETERS\n")
            f.write("-" * 20 + "\n")
            f.write(f"window_size: {int(best['window_size'])}\n")
            f.write(f"n_liquidity_days: {int(best['n_liquidity_days'])}\n")
            f.write(f"n_liquidity_assets: {int(best['n_liquidity_assets'])}\n")
            f.write(f"n_selected_assets: {int(best['n_selected_assets'])}\n")
            f.write(f"\nOptimized Performance:\n")
            f.write(f"  Risk-Adjusted Score: {best['risk_adjusted_score']:.4f}\n")
            f.write(f"  Sharpe Ratio: {best['sharpe_ratio']:.3f}\n")
            f.write(f"  Calmar Ratio: {best['calmar_ratio']:.3f}\n")
            f.write(f"  Max Drawdown: {best['max_drawdown']:.2%}\n")
            f.write(f"  Win Rate: {best['win_rate']:.1f}%\n")
            f.write(f"  Total Trades: {int(best['total_trades'])}\n")
            
            improvement = ((best['risk_adjusted_score'] / current_metrics['risk_adjusted_score']) - 1) * 100
            f.write(f"\nImprovement: {improvement:.1f}%\n\n")
        
        # Walk-forward analysis
        f.write("WALK-FORWARD ANALYSIS\n")
        f.write("-" * 25 + "\n")
        f.write(f"Average Return: {wf_results['avg_return']:.2%}\n")
        f.write(f"Return Volatility: {wf_results['std_return']:.2%}\n")
        f.write(f"Average Sharpe: {wf_results['avg_sharpe']:.3f}\n")
        f.write(f"Consistency Score: {wf_results['consistency_score']:.1%}\n")
        f.write(f"Worst Max Drawdown: {wf_results['worst_max_dd']:.2%}\n\n")
        
        # Parameter sensitivity
        if sensitivity:
            f.write("PARAMETER SENSITIVITY\n")
            f.write("-" * 20 + "\n")
            f.write("Correlations with Risk-Adjusted Score:\n")
            for param, corr in sensitivity['correlations'].items():
                f.write(f"  {param}: {corr['risk_adjusted_score']:.3f}\n")
            f.write("\n")
        
        # Top 10 parameter combinations
        if not fine_results.empty:
            f.write("TOP 10 PARAMETER COMBINATIONS\n")
            f.write("-" * 30 + "\n")
            top_10 = fine_results.head(10)
            for i, (_, row) in enumerate(top_10.iterrows(), 1):
                f.write(f"{i:2d}. WS:{int(row['window_size']):2d} LD:{int(row['n_liquidity_days']):3d} "
                       f"LA:{int(row['n_liquidity_assets']):3d} SA:{int(row['n_selected_assets']):2d} "
                       f"Score:{row['risk_adjusted_score']:.4f}\n")
    
    print(f"Detailed report saved to: {report_file}")


def create_optimization_plots(optimizer, coarse_results, fine_results):
    """Create visualization plots for optimization results."""
    
    if fine_results.empty:
        print("No results to plot.")
        return
    
    # Create figure with subplots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Parameter Optimization Results', fontsize=16, fontweight='bold')
    
    # Plot 1: Risk-Adjusted Score vs Parameters
    ax1 = axes[0, 0]
    scatter = ax1.scatter(fine_results['window_size'], fine_results['risk_adjusted_score'], 
                         c=fine_results['sharpe_ratio'], cmap='viridis', alpha=0.7)
    ax1.set_xlabel('Window Size')
    ax1.set_ylabel('Risk-Adjusted Score')
    ax1.set_title('Score vs Window Size')
    plt.colorbar(scatter, ax=ax1, label='Sharpe Ratio')
    
    # Plot 2: Sharpe vs Drawdown
    ax2 = axes[0, 1]
    scatter2 = ax2.scatter(fine_results['max_drawdown'], fine_results['sharpe_ratio'],
                          c=fine_results['risk_adjusted_score'], cmap='plasma', alpha=0.7)
    ax2.set_xlabel('Max Drawdown')
    ax2.set_ylabel('Sharpe Ratio')
    ax2.set_title('Risk-Return Profile')
    plt.colorbar(scatter2, ax=ax2, label='Risk-Adjusted Score')
    
    # Plot 3: Parameter Distribution (Top 20%)
    ax3 = axes[0, 2]
    top_20_pct = fine_results.head(int(len(fine_results) * 0.2))
    param_data = [top_20_pct['window_size'], top_20_pct['n_liquidity_days'], 
                  top_20_pct['n_liquidity_assets'], top_20_pct['n_selected_assets']]
    param_labels = ['Window\nSize', 'Liquidity\nDays', 'Liquidity\nAssets', 'Selected\nAssets']
    ax3.boxplot(param_data, labels=param_labels)
    ax3.set_title('Top 20% Parameter Distribution')
    ax3.tick_params(axis='x', rotation=45)
    
    # Plot 4: Performance Metrics Heatmap
    ax4 = axes[1, 0]
    metrics_cols = ['risk_adjusted_score', 'sharpe_ratio', 'calmar_ratio', 'win_rate', 'profit_factor']
    top_10 = fine_results.head(10)[metrics_cols]
    sns.heatmap(top_10.T, annot=True, fmt='.3f', cmap='RdYlGn', ax=ax4)
    ax4.set_title('Top 10 Performance Heatmap')
    ax4.set_xlabel('Rank')
    
    # Plot 5: Liquidity Assets vs Selected Assets
    ax5 = axes[1, 1]
    scatter3 = ax5.scatter(fine_results['n_liquidity_assets'], fine_results['n_selected_assets'],
                          c=fine_results['risk_adjusted_score'], cmap='coolwarm', alpha=0.7)
    ax5.set_xlabel('Liquidity Assets')
    ax5.set_ylabel('Selected Assets')
    ax5.set_title('Asset Selection Parameters')
    plt.colorbar(scatter3, ax=ax5, label='Risk-Adjusted Score')
    
    # Plot 6: Score Distribution
    ax6 = axes[1, 2]
    ax6.hist(fine_results['risk_adjusted_score'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax6.axvline(fine_results['risk_adjusted_score'].mean(), color='red', linestyle='--', 
                label=f'Mean: {fine_results["risk_adjusted_score"].mean():.3f}')
    ax6.set_xlabel('Risk-Adjusted Score')
    ax6.set_ylabel('Frequency')
    ax6.set_title('Score Distribution')
    ax6.legend()
    
    plt.tight_layout()
    
    # Save plot
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    plot_file = f'optimization_plots_{timestamp}.png'
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Optimization plots saved to: {plot_file}")


if __name__ == "__main__":
    main()
